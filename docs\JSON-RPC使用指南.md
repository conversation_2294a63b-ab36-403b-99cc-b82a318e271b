# JSON-RPC使用指南

## 概述

WebSDK提供统一的JSON-RPC API：`sdk.sendJsonRpcMessage(message, options)`，让用户完全控制JSON-RPC消息格式和请求ID。

## 为什么使用JSON-RPC API？

### 设计优势

WebSDK的JSON-RPC API具有以下优势：

1. **完全控制请求ID**：用户可以自定义请求ID，便于调试和监控
2. **标准JSON-RPC 2.0格式**：完全符合JSON-RPC 2.0规范
3. **灵活的消息格式**：用户可以发送完整的JSON-RPC消息
4. **更好的可观测性**：便于请求追踪和问题排查

### 核心功能

`sendJsonRpcMessage`方法提供：

- 完全控制请求ID
- 发送标准的JSON-RPC 2.0消息
- 自主管理请求-响应配对
- 更好的调试和监控能力

## 基本使用

### 1. JSON-RPC消息发送

```typescript
import { init } from 'web-service-api-sdk';

const sdk = await init({
  hksttUrl: 'ws://localhost:8001',
  aiServerUrl: 'http://localhost:8080',
  debug: true
});

// 发送完整的JSON-RPC消息
const response = await sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'speak',
  params: {
    text: '您好，欢迎使用我们的服务',
    delay: 1000,
    display: true
  },
  id: 'my-speak-request-001'  // 用户完全控制ID
});

console.log('响应:', response);
```

### 2. 请求ID管理

```typescript
// 使用自定义ID进行请求跟踪
const requestId = `speak-${Date.now()}`;

try {
  const response = await sdk.sendJsonRpcMessage({
    jsonrpc: '2.0',
    method: 'speak',
    params: { text: '测试消息' },
    id: requestId
  });
  
  console.log(`请求 ${requestId} 成功:`, response);
} catch (error) {
  console.error(`请求 ${requestId} 失败:`, error);
}
```

### 3. 批量请求管理

```typescript
// 发送多个相关请求
const sessionId = 'user-session-123';
const requests = [
  {
    jsonrpc: '2.0' as const,
    method: 'updateBackgroundInfo',
    params: { sessionId, page: 'login', status: 'active' },
    id: `${sessionId}-bg-update`
  },
  {
    jsonrpc: '2.0' as const,
    method: 'addMessages',
    params: {
      sessionId,
      messages: [{ role: 'user', content: '我想查询余额' }]
    },
    id: `${sessionId}-add-msg`
  }
];

// 并发发送
const responses = await Promise.all(
  requests.map(req => sdk.sendJsonRpcMessage(req))
);

console.log('所有请求完成:', responses);
```

## 高级功能

### 1. 可选验证

```typescript
// 跳过方法名验证（用于扩展方法）
await sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'customMethod',
  params: { data: 'test' },
  id: 'custom-001'
}, {
  validateMethod: false,  // 跳过方法名验证
  validateParams: false,  // 跳过参数验证
  timeout: 15000         // 自定义超时
});
```

### 2. 错误处理

```typescript
try {
  await sdk.sendJsonRpcMessage({
    jsonrpc: '2.0',
    method: 'speak',
    params: { text: '' },  // 无效参数
    id: 'error-test'
  });
} catch (error) {
  if (error.message.includes('参数格式不正确')) {
    console.log('参数验证失败');
  } else if (error.message.includes('请求ID冲突')) {
    console.log('ID已被使用');
  }
}
```

### 3. 请求状态监控

```typescript
// 发送请求前检查pending状态
const pendingRequests = sdk.getPendingRequestsInfo();
console.log('当前待处理请求:', pendingRequests);

// 发送请求
const requestId = 'monitor-test-001';
const promise = sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'speak',
  params: { text: '监控测试' },
  id: requestId
});

// 检查请求是否在pending列表中
const updated = sdk.getPendingRequestsInfo();
const myRequest = updated.find(req => req.id === requestId);
console.log('我的请求状态:', myRequest);

await promise;
```

## 支持的方法

当前SDK支持以下JSON-RPC方法：

### 1. speak - 语音播报

```typescript
await sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'speak',
  params: {
    text: '要播报的文本',      // 必需
    delay: 1000,             // 可选，延时毫秒数
    display: true            // 可选，是否显示在气泡中
  },
  id: 'speak-001'
});
```

### 2. updateBackgroundInfo - 更新背景信息

```typescript
await sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'updateBackgroundInfo',
  params: {
    sessionId: 'session-123',  // 可选，会话ID
    page: 'login-page',        // 可选，当前页面
    status: 'active'           // 可选，当前状态
  },
  id: 'bg-update-001'
});
```

### 3. addMessages - 添加对话消息

```typescript
await sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'addMessages',
  params: {
    sessionId: 'session-123',  // 可选，会话ID
    messages: [                // 必需，消息数组
      { role: 'user', content: '用户消息' },
      { role: 'assistant', content: 'AI回复' }
    ]
  },
  id: 'add-msg-001'
});
```

### 4. pushBizData - 推送业务数据

```typescript
await sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'pushBizData',
  params: {
    key: 'userProfile',        // 必需，数据键名
    data: {                    // 必需，业务数据
      userId: '123',
      name: '张三',
      balance: 1000
    }
  },
  id: 'push-data-001'
});
```

## 向后兼容性

原有的`sendRequest`方法完全保持兼容：

```typescript
// 旧方式（仍然支持）
const response1 = await sdk.sendRequest('speak', {
  text: '你好'
});

// 新方式（推荐）
const response2 = await sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'speak',
  params: { text: '你好' },
  id: 'my-custom-id'
});
```

## 最佳实践

### 1. 请求ID命名规范

```typescript
// 推荐的ID命名模式
const requestId = `${method}-${sessionId}-${timestamp}`;
const requestId2 = `speak-user123-${Date.now()}`;
const requestId3 = `bg-update-login-page-001`;
```

### 2. 错误重试机制

```typescript
async function sendWithRetry(message, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      // 每次重试使用新的ID
      const retryMessage = {
        ...message,
        id: `${message.id}-retry-${i}`
      };
      
      return await sdk.sendJsonRpcMessage(retryMessage);
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}
```

### 3. 请求去重

```typescript
const sentRequests = new Set();

function sendUniqueRequest(message) {
  if (sentRequests.has(message.id)) {
    throw new Error(`请求ID ${message.id} 已发送过`);
  }
  
  sentRequests.add(message.id);
  return sdk.sendJsonRpcMessage(message);
}
```

## 最佳实践

### 请求ID命名规范

```typescript
// 推荐的请求ID格式
const requestId = `${method}-${timestamp}-${sequence}`;

// 示例
const result = await sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'speak',
  params: { text: '你好' },
  id: `speak-${Date.now()}-001`
});
```

### 请求跟踪和监控

```typescript
// 使用有意义的请求ID进行跟踪
const myRequestId = 'user-greeting-request';
await sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'speak',
  params: { text: '你好' },
  id: myRequestId
});

// 可以跟踪这个特定的请求
console.log(`请求 ${myRequestId} 已发送`);
```
