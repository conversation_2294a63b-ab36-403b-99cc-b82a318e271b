/**
 * 简化的聊天界面组件
 * 专门为ChatWidget设计，保持与Lit版本样式完全一致
 */

import { Send } from 'lucide-react';
import React, { useState, useEffect, useRef } from 'react';

import { WebSDK } from '../../core/WebSDK';
import { Logger } from '../../utils/Logger';

// 添加滚动条样式和流式消息动画
const scrollbarStyles = `
  .chat-messages-container::-webkit-scrollbar {
    width: 6px;
  }
  .chat-messages-container::-webkit-scrollbar-track {
    background: transparent;
  }
  .chat-messages-container::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
  }
  .chat-messages-container::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }
  .chat-messages-container.dark::-webkit-scrollbar-thumb {
    background: #4b5563;
  }
  .chat-messages-container.dark::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
  }

  /* 流式消息动画 */
  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.3; }
    100% { opacity: 1; }
  }

  .streaming-message {
    animation: pulse 1.5s ease-in-out infinite;
  }
`;

// 注入样式
if (typeof document !== 'undefined' && !document.getElementById('chat-scrollbar-styles')) {
  const styleElement = document.createElement('style');
  styleElement.id = 'chat-scrollbar-styles';
  styleElement.textContent = scrollbarStyles;
  document.head.appendChild(styleElement);
}

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  sessionId: string;
  status: 'pending' | 'sending' | 'sent' | 'delivered' | 'failed' | 'streaming';
  createdAt: number;
  updatedAt: number;
}

interface SimpleChatInterfaceProps {
  sdk: WebSDK;
  sessionId: string;
  messages: Message[];
  isLoading: boolean;
  onSendMessage: (text: string) => void;
  theme?: 'light' | 'dark';
}

export const SimpleChatInterface: React.FC<SimpleChatInterfaceProps> = ({
  sdk,
  sessionId,
  messages,
  isLoading,
  onSendMessage,
  theme = 'light',
}) => {
  const [inputValue, setInputValue] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const loggerRef = useRef(Logger.getInstance({ prefix: 'SimpleChatInterface' }));

  // 避免未使用变量警告，这些参数保留用于未来扩展
  // 移除重复的配置日志以减少噪音
  if (process.env.NODE_ENV === 'development') {
    console.debug('SimpleChatInterface配置:', {
      sdk: !!sdk,
      sessionId,
      logger: !!loggerRef.current,
    });
  }

  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // 发送消息
  const handleSend = () => {
    const text = inputValue.trim();
    if (!text || isLoading) {
      return;
    }

    onSendMessage(text);
    setInputValue('');
  };

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  // 渲染单个消息气泡
  const renderMessage = (message: Message) => {
    const isUser = message.role === 'user';
    const isSystem = message.role === 'system';
    const isDark = theme === 'dark';

    if (isSystem) {
      return (
        <div
          key={message.id}
          style={{
            display: 'flex',
            justifyContent: 'center',
            padding: '8px 0',
          }}
        >
          <div
            style={{
              backgroundColor: '#fef3c7',
              color: '#92400e',
              padding: '4px 12px',
              borderRadius: '9999px',
              fontSize: '12px',
            }}
          >
            {message.content}
          </div>
        </div>
      );
    }

    return (
      <div
        key={message.id}
        style={{
          display: 'flex',
          alignItems: 'flex-start',
          gap: '8px',
          maxWidth: '85%',
          alignSelf: isUser ? 'flex-end' : 'flex-start',
          flexDirection: isUser ? 'row-reverse' : 'row',
          marginBottom: '4px', // 增加消息间距
          flexShrink: 0, // 防止消息被压缩
        }}
      >
        {/* 头像 */}
        <div
          style={{
            width: '32px',
            height: '32px',
            backgroundColor: '#3b82f6',
            color: 'white',
            fontSize: '12px',
            fontWeight: 'bold',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexShrink: 0,
          }}
        >
          {isUser ? '我' : 'AI'}
        </div>

        {/* 消息气泡 */}
        <div
          className={message.status === 'streaming' ? 'streaming-message' : ''}
          style={{
            backgroundColor: isUser ? '#3b82f6' : isDark ? '#374151' : '#f3f4f6',
            color: isUser ? 'white' : isDark ? '#e5e7eb' : '#111827',
            padding: '12px 16px',
            borderRadius: '18px',
            fontSize: '14px',
            lineHeight: '1.5',
            position: 'relative',
            wordWrap: 'break-word', // 长文本自动换行
            wordBreak: 'break-word', // 处理长单词
            maxWidth: '100%', // 确保不超出容器
            minWidth: 0, // 允许收缩
          }}
        >
          {message.content}
          {message.status === 'streaming' && (
            <span
              style={{
                color: isUser ? 'rgba(255, 255, 255, 0.7)' : isDark ? '#9ca3af' : '#6b7280',
                marginLeft: '4px',
              }}
            >
              ▋
            </span>
          )}
          <div
            style={{
              fontSize: '12px',
              color: isUser ? 'rgba(255, 255, 255, 0.7)' : isDark ? '#9ca3af' : '#6b7280',
              marginTop: '4px',
            }}
          >
            {new Date(message.createdAt).toLocaleTimeString('zh-CN', {
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
            })}
            {message.status === 'streaming' && (
              <span style={{ marginLeft: '4px' }}>正在输入...</span>
            )}
          </div>
        </div>
      </div>
    );
  };

  const isDark = theme === 'dark';

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        backgroundColor: isDark ? '#1f2937' : '#ffffff',
        overflow: 'hidden', // 防止整体滚动
      }}
    >
      {/* 消息列表 - 使用固定高度和滚动 */}
      <div
        className={`chat-messages-container ${isDark ? 'dark' : ''}`}
        style={{
          flex: 1,
          padding: '16px',
          overflowY: 'auto',
          overflowX: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          gap: '12px',
          minHeight: 0,
          maxHeight: '100%', // 确保不超出容器
          // Firefox滚动条样式
          scrollbarWidth: 'thin',
          scrollbarColor: isDark ? '#4b5563 #374151' : '#d1d5db #f9fafb',
        }}
      >
        {messages.map(renderMessage)}
        {isLoading && (
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              padding: '8px 0',
            }}
          >
            <div
              style={{
                fontSize: '14px',
                color: '#6b7280',
              }}
            >
              AI正在思考中...
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <div
        style={{
          flexShrink: 0,
          backgroundColor: isDark ? '#1f2937' : '#f9fafb',
          borderTop: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
        }}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            padding: '16px',
            gap: '12px',
          }}
        >
          <input
            type="text"
            value={inputValue}
            onChange={e => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="点击右侧按钮开始对话"
            disabled={isLoading}
            style={{
              flex: 1,
              backgroundColor: isDark ? '#374151' : '#ffffff',
              border: `1px solid ${isDark ? '#4b5563' : '#d1d5db'}`,
              color: isDark ? '#ffffff' : '#111827',
              padding: '12px 16px',
              borderRadius: '8px',
              fontSize: '14px',
              outline: 'none',
              transition: 'border-color 0.2s',
            }}
            onFocus={e => {
              e.target.style.borderColor = '#3b82f6';
            }}
            onBlur={e => {
              e.target.style.borderColor = isDark ? '#4b5563' : '#d1d5db';
            }}
          />
          <button
            onClick={handleSend}
            disabled={!inputValue.trim() || isLoading}
            style={{
              backgroundColor: '#3b82f6',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: inputValue.trim() && !isLoading ? 'pointer' : 'not-allowed',
              opacity: inputValue.trim() && !isLoading ? 1 : 0.5,
              transition: 'opacity 0.2s',
            }}
          >
            <Send size={16} />
          </button>
        </div>
      </div>
    </div>
  );
};
