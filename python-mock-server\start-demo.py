#!/usr/bin/env python3
"""
WebSDK Demo 启动脚本
快速启动完整的演示环境
"""

import asyncio
import sys
import os
import webbrowser
import time
from pathlib import Path

# 当前脚本在 python-mock-server 目录中
# 导入同目录下的模块
try:
    from test_hkstt_server import HKSTTServerTest, interactive_test
    from ai_server import AIServerProxy
    from adapters.ai_response_adapter import AIResponseAdapter, create_ai_response
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保在 python-mock-server 目录中运行此脚本")
    sys.exit(1)


def print_banner():
    """打印启动横幅"""
    print("=" * 70)
    print("🚀 WebSDK 完整演示环境")
    print("=" * 70)
    print("📋 包含服务:")
    print("   • HKSTT语音识别服务 (WebSocket, 端口8001)")
    print("   • AI对话服务 (HTTP+SSE, 端口8080)")
    print("   • AI响应适配器 (AI格式 → JSON-RPC格式)")
    print("   • HTML演示页面 (自动打开浏览器)")
    print("-" * 70)
    print("🎯 演示流程:")
    print("   1. 等待SDK连接到服务器")
    print("   2. 在CLI中选择'1'模拟人脸检测")
    print("   3. 数字人页面自动弹出")
    print("   4. 选择'3'发送语音识别结果")
    print("   5. AI自动回复并显示对话")
    print("   6. 点击'返回'按钮回到主页面")
    print("-" * 70)
    print("🚀 正在启动服务...")
    print()


async def start_ai_server():
    """启动AI服务器"""
    try:
        ai_server = AIServerProxy(port=8080)
        await ai_server.start_server()
    except Exception as e:
        print(f"❌ AI服务器启动失败: {e}")


def open_demo_page():
    """打开演示页面"""
    # 从 python-mock-server 目录向上一级找到 demo 目录
    demo_path = Path(__file__).parent.parent / "demo" / "simple-demo.html"
    if demo_path.exists():
        demo_url = f"file://{demo_path.absolute()}"
        print(f"🌐 正在打开演示页面: {demo_url}")
        try:
            webbrowser.open(demo_url)
            print("✅ 演示页面已在浏览器中打开")
        except Exception as e:
            print(f"⚠️ 无法自动打开浏览器: {e}")
            print(f"请手动打开: {demo_url}")
    else:
        print(f"❌ 演示页面不存在: {demo_path}")
        print("请确保在项目根目录的 python-mock-server 子目录中运行此脚本")


def test_adapter_demo():
    """演示适配器功能"""
    print("\n🔄 AI响应适配器演示")
    print("-" * 40)

    adapter = AIResponseAdapter()
    request_id = "demo_req_001"
    session_id = "demo_session_123"

    # 演示流式响应适配
    print("📡 流式响应适配演示:")
    messages = ["你好，", "我是AI助手，", "很高兴为您服务！"]

    for i, msg in enumerate(messages):
        ai_response = create_ai_response(2, msg, session_id)
        adapted = adapter.adapt_response(ai_response, request_id)

        if adapted:
            print(f"  {i+1}. 原始: code=2, message='{msg}'")
            print(f"     适配: method='{adapted['method']}', message='{adapted['params']['message']}'")

    # 演示最终响应适配
    print("\n✅ 最终响应适配演示:")
    final_ai_response = create_ai_response(0, "", session_id)  # 空消息，使用缓冲
    final_adapted = adapter.adapt_response(final_ai_response, request_id)

    if final_adapted:
        print(f"  原始: code=0, message=''")
        print(f"  适配: id='{final_adapted['id']}', message='{final_adapted['result']['message']}'")
        print(f"  消息合并: {''.join(messages)}")

    # 显示适配器状态
    status = adapter.get_status()
    print(f"\n📊 适配器状态: 活跃上下文={status['active_contexts']}")
    print("-" * 40)


async def main():
    """主函数"""
    print_banner()

    # 创建HKSTT服务器实例
    hkstt_server = HKSTTServerTest(port=8001)

    try:
        # 在后台启动HKSTT服务器
        print("📡 启动HKSTT服务器...")
        hkstt_task = asyncio.create_task(hkstt_server.start_server())

        # 在后台启动AI服务器
        print("🤖 启动AI服务器...")
        ai_task = asyncio.create_task(start_ai_server())

        # 等待服务器启动
        await asyncio.sleep(2)

        print("✅ 所有服务器启动成功!")
        print(f"📡 HKSTT服务器: ws://localhost:{hkstt_server.port}")
        print(f"🤖 AI服务器: http://localhost:8080")
        print(f"🔄 适配器状态: http://localhost:8080/adapter/status")
        print()

        # 演示适配器功能
        test_adapter_demo()

        # 打开演示页面
        open_demo_page()
        
        print("📖 使用说明:")
        print("   1. 等待浏览器页面显示'已连接到语音识别服务'")
        print("   2. 使用下面的CLI菜单进行测试")
        print("   3. 建议测试流程: 选择1 → 选择3 → 观察页面变化")
        print("   4. 也可以在页面中手动发送AI消息测试")
        print()

        # 启动交互式测试
        test_task = asyncio.create_task(interactive_test(hkstt_server))

        # 等待任一任务完成
        done, pending = await asyncio.wait(
            [hkstt_task, ai_task, test_task],
            return_when=asyncio.FIRST_COMPLETED
        )

        # 取消未完成的任务
        for task in pending:
            task.cancel()

        # 取消未完成的任务
        for task in pending:
            task.cancel()

    except KeyboardInterrupt:
        print("\n👋 收到中断信号，正在停止所有服务器...")
    except Exception as e:
        print(f"\n❌ 服务器运行错误: {e}")
    finally:
        hkstt_server.stop()
        print("✅ 所有服务器已停止")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1)
