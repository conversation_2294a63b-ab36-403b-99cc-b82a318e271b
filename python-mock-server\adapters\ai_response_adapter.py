#!/usr/bin/env python3
"""
AI响应适配器
将AI服务器的原始响应格式转换为JSON-RPC 2.0标准格式
严格遵循docs/json-rpc.md文档规范
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any


class RequestContext:
    """请求上下文"""
    def __init__(self, request_id: str, session_id: str = ""):
        self.request_id = request_id
        self.session_id = session_id
        self.start_time = datetime.now()
        self.message_buffer: List[str] = []
        self.status = "active"


class AIResponseAdapter:
    """AI服务器响应适配器"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.request_contexts: Dict[str, RequestContext] = {}
        
        # 配置参数
        self.max_buffer_size = 100  # 最大缓冲区大小
        self.context_timeout = 300  # 上下文超时时间（秒）
        
        self.logger.info("AI响应适配器已初始化")
    
    def register_request(self, request_id: str, session_id: str = "") -> None:
        """
        注册请求上下文
        
        Args:
            request_id: 请求ID
            session_id: 会话ID
        """
        context = RequestContext(request_id, session_id)
        self.request_contexts[request_id] = context
        
        self.logger.debug(f"注册请求上下文: {request_id} -> {session_id}")
    
    def adapt_response(self, ai_response: Dict[str, Any], request_id: str) -> Optional[Dict[str, Any]]:
        """
        适配AI服务器响应为JSON-RPC格式
        
        Args:
            ai_response: AI服务器原始响应
            request_id: 请求ID
            
        Returns:
            转换后的JSON-RPC格式响应，如果不需要转发则返回None
        """
        try:
            code = ai_response.get("code")
            data = ai_response.get("data", {})
            error_msg = ai_response.get("errorMsg", "")
            
            self.logger.debug(f"适配AI响应: code={code}, requestId={request_id}")
            
            # 获取或创建请求上下文
            context = self._get_or_create_context(request_id, data.get("sessionId", ""))
            
            if code == 2:  # 流式响应进行中
                return self._create_progress_notification(data, context)
            elif code == 0:  # 流式响应完成
                return self._create_final_response(data, context)
            elif code == 1:  # 中间状态或错误
                if error_msg:
                    return self._create_error_response(error_msg, context)
                else:
                    # 中间状态响应，过滤掉不转发
                    self.logger.debug(f"过滤中间状态响应 (code=1): {data.get('message', '')}")
                    return None
            else:
                return self._create_error_response(f"未知响应代码: {code}", context)
                
        except Exception as e:
            self.logger.error(f"适配响应失败: {e}")
            return self._create_error_response(f"适配器内部错误: {str(e)}", 
                                             self._get_or_create_context(request_id))
    
    def _get_or_create_context(self, request_id: str, session_id: str = "") -> RequestContext:
        """获取或创建请求上下文"""
        if request_id not in self.request_contexts:
            self.register_request(request_id, session_id)
        
        context = self.request_contexts[request_id]
        
        # 更新会话ID（如果提供了新的）
        if session_id and not context.session_id:
            context.session_id = session_id
            
        return context
    
    def _create_progress_notification(self, data: Dict[str, Any], context: RequestContext) -> Optional[Dict[str, Any]]:
        """
        创建流式进度通知
        遵循docs/json-rpc.md中的notifications/progress格式
        """
        message = data.get("message", "")
        session_id = data.get("sessionId", context.session_id)

        # 过滤空消息，避免发送无意义的通知
        if not message or not message.strip():
            self.logger.debug(f"过滤空消息: requestId={context.request_id}")
            return None

        # 更新上下文中的sessionId（如果AI服务返回了新的sessionId）
        if session_id and session_id != context.session_id:
            context.session_id = session_id
            self.logger.debug(f"更新上下文sessionId: {context.request_id} -> {session_id}")

        # 缓冲消息
        context.message_buffer.append(message)

        # 限制缓冲区大小
        if len(context.message_buffer) > self.max_buffer_size:
            context.message_buffer = context.message_buffer[-self.max_buffer_size:]
            self.logger.warning(f"消息缓冲区已满，丢弃旧消息: {context.request_id}")

        # 创建JSON-RPC通知，严格按照文档规范
        notification = {
            "jsonrpc": "2.0",
            "method": "notifications/progress",
            "params": {
                "message": message,
                "requestId": context.request_id,
                "sessionId": context.session_id
            }
        }

        self.logger.debug(f"创建进度通知: requestId={context.request_id}, sessionId={context.session_id}, message={message[:50]}...")
        return notification
    
    def _create_final_response(self, data: Dict[str, Any], context: RequestContext) -> Dict[str, Any]:
        """
        创建最终响应
        遵循JSON-RPC 2.0标准响应格式
        """
        # 合并缓冲的消息
        buffered_message = "".join(context.message_buffer)

        # 使用AI返回的消息或缓冲的完整消息
        final_message = data.get("message", "") or buffered_message
        session_id = data.get("sessionId", context.session_id)

        # 更新上下文中的sessionId（如果AI服务返回了新的sessionId）
        if session_id and session_id != context.session_id:
            context.session_id = session_id
            self.logger.debug(f"最终响应更新sessionId: {context.request_id} -> {session_id}")

        # 创建JSON-RPC响应，严格按照文档规范
        response = {
            "jsonrpc": "2.0",
            "id": context.request_id,
            "result": {
                "message": final_message,
                "sessionId": context.session_id
            }
        }

        self.logger.info(f"创建最终响应: requestId={context.request_id}, sessionId={context.session_id}, 消息长度={len(final_message)}")

        # 清理上下文
        self._cleanup_context(context.request_id)

        return response
    
    def _create_error_response(self, error_msg: str, context: RequestContext) -> Dict[str, Any]:
        """
        创建错误响应
        遵循JSON-RPC 2.0标准错误格式
        """
        # 创建JSON-RPC错误响应
        response = {
            "jsonrpc": "2.0",
            "id": context.request_id,
            "error": {
                "code": -32603,  # 内部错误
                "message": error_msg,
                "data": {
                    "timestamp": datetime.now().isoformat(),
                    "source": "ai_server_adapter",
                    "sessionId": context.session_id
                }
            }
        }
        
        self.logger.error(f"创建错误响应: {context.request_id}, 错误: {error_msg}")
        
        # 清理上下文
        self._cleanup_context(context.request_id)
        
        return response
    
    def _cleanup_context(self, request_id: str) -> None:
        """清理请求上下文"""
        if request_id in self.request_contexts:
            del self.request_contexts[request_id]
            self.logger.debug(f"清理请求上下文: {request_id}")
    
    def cleanup_expired_contexts(self) -> int:
        """
        清理过期的请求上下文
        
        Returns:
            清理的上下文数量
        """
        now = datetime.now()
        expired_contexts = []
        
        for request_id, context in self.request_contexts.items():
            age = (now - context.start_time).total_seconds()
            if age > self.context_timeout:
                expired_contexts.append(request_id)
        
        for request_id in expired_contexts:
            self._cleanup_context(request_id)
        
        if expired_contexts:
            self.logger.info(f"清理过期上下文: {len(expired_contexts)}个")
        
        return len(expired_contexts)
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取适配器状态
        
        Returns:
            适配器状态信息
        """
        now = datetime.now()
        oldest_age = 0
        
        if self.request_contexts:
            oldest_context = min(self.request_contexts.values(), key=lambda c: c.start_time)
            oldest_age = (now - oldest_context.start_time).total_seconds()
        
        return {
            "active_contexts": len(self.request_contexts),
            "oldest_context_age": oldest_age,
            "max_buffer_size": self.max_buffer_size,
            "context_timeout": self.context_timeout,
        }
    
    def clear_all_contexts(self) -> int:
        """
        清理所有上下文
        
        Returns:
            清理的上下文数量
        """
        count = len(self.request_contexts)
        self.request_contexts.clear()
        
        self.logger.info(f"清理所有上下文: {count}个")
        return count
    
    def validate_ai_response(self, ai_response: Dict[str, Any]) -> bool:
        """
        验证AI服务器响应格式
        
        Args:
            ai_response: AI服务器响应
            
        Returns:
            是否为有效格式
        """
        if not isinstance(ai_response, dict):
            return False
        
        # 检查必需字段
        if "code" not in ai_response:
            return False
        
        if "data" not in ai_response or not isinstance(ai_response["data"], dict):
            return False
        
        # 检查code值
        code = ai_response["code"]
        if not isinstance(code, int) or code not in [0, 1, 2]:
            return False
        
        return True
    
    def create_test_response(self, request_id: str, message: str, is_final: bool = False) -> Optional[Dict[str, Any]]:
        """
        创建测试响应（用于单元测试）

        Args:
            request_id: 请求ID
            message: 消息内容
            is_final: 是否为最终响应

        Returns:
            JSON-RPC格式的响应，如果是空消息的进度通知则返回None
        """
        context = self._get_or_create_context(request_id, f"session_{request_id}")

        if is_final:
            return self._create_final_response({"message": message, "sessionId": context.session_id}, context)
        else:
            return self._create_progress_notification({"message": message, "sessionId": context.session_id}, context)


# 工具函数
def create_ai_response(code: int, message: str, session_id: str = "", error_msg: str = "") -> Dict[str, Any]:
    """
    创建AI服务器格式的响应（用于测试）
    
    Args:
        code: 响应代码 (0=完成, 1=中间状态, 2=进行中)
        message: 消息内容
        session_id: 会话ID
        error_msg: 错误消息
        
    Returns:
        AI服务器格式的响应
    """
    return {
        "code": code,
        "data": {
            "sessionId": session_id,
            "source": 6,
            "message": message,
            "extra": None
        },
        "errorMsg": error_msg,
        "language": "zh-CN"
    }


def validate_jsonrpc_response(response: Dict[str, Any]) -> bool:
    """
    验证JSON-RPC响应格式
    
    Args:
        response: 响应数据
        
    Returns:
        是否为有效的JSON-RPC格式
    """
    if not isinstance(response, dict):
        return False
    
    # 检查jsonrpc字段
    if response.get("jsonrpc") != "2.0":
        return False
    
    # 检查是否为通知或响应
    if "method" in response:
        # 通知格式
        return "params" in response and "id" not in response
    elif "id" in response:
        # 响应格式
        return "result" in response or "error" in response
    
    return False
