/**
 * 消息管理服务
 * 负责管理聊天消息的存储、检索和状态管理
 */

import { EventBus } from '../core/EventBus';
import { generateRequestId } from '../utils/helpers';
import { Logger, LogLevel } from '../utils/Logger';

/**
 * 消息类型
 */
export type MessageRole = 'user' | 'assistant' | 'system';

/**
 * 消息状态
 */
export type MessageStatus = 'pending' | 'sending' | 'sent' | 'delivered' | 'failed' | 'streaming';

/**
 * 聊天消息
 */
export interface ChatMessage {
  /** 消息ID */
  id: string;
  /** 消息角色 */
  role: MessageRole;
  /** 消息内容 */
  content: string;
  /** 会话ID */
  sessionId: string;
  /** 消息状态 */
  status: MessageStatus;
  /** 创建时间 */
  createdAt: number;
  /** 更新时间 */
  updatedAt: number;
  /** 元数据 */
  metadata?: Record<string, unknown>;
}

/**
 * 消息历史
 */
export interface MessageHistory {
  /** 会话ID */
  sessionId: string;
  /** 消息列表 */
  messages: ChatMessage[];
  /** 总消息数 */
  totalCount: number;
  /** 最后更新时间 */
  lastUpdatedAt: number;
}

/**
 * 消息服务配置
 */
export interface MessageServiceConfig {
  /** 最大消息历史数量 */
  maxHistorySize?: number;
  /** 是否启用持久化 */
  enablePersistence?: boolean;
  /** 消息过期时间（毫秒） */
  messageExpireTime?: number;
}

/**
 * 消息管理服务
 */
export class MessageService {
  private eventBus: EventBus;
  private logger: Logger;
  private config: Required<MessageServiceConfig>;

  // 消息存储
  private messageHistories: Map<string, MessageHistory> = new Map();
  private messageIndex: Map<string, ChatMessage> = new Map();

  // 统计信息
  private stats = {
    totalMessages: 0,
    totalSessions: 0,
    messagesByRole: {
      user: 0,
      assistant: 0,
      system: 0,
    } as Record<MessageRole, number>,
    messagesByStatus: {
      pending: 0,
      sending: 0,
      sent: 0,
      delivered: 0,
      failed: 0,
      streaming: 0,
    } as Record<MessageStatus, number>,
  };

  constructor(eventBus: EventBus, config: MessageServiceConfig = {}) {
    this.eventBus = eventBus;
    this.logger = Logger.getInstance({
      level: LogLevel.INFO,
      prefix: 'MessageService',
    });

    // 合并配置
    this.config = {
      maxHistorySize: 1000,
      enablePersistence: false,
      messageExpireTime: 24 * 60 * 60 * 1000, // 24小时
      ...config,
    };

    this.setupEventListeners();
  }

  // ============================================================================
  // 消息管理
  // ============================================================================

  /**
   * 添加消息
   */
  public addMessage(
    role: MessageRole,
    content: string,
    sessionId: string,
    metadata?: Record<string, unknown>
  ): ChatMessage {
    const messageId = generateRequestId();
    const now = Date.now();

    const message: ChatMessage = {
      id: messageId,
      role,
      content,
      sessionId,
      status: 'pending',
      createdAt: now,
      updatedAt: now,
      ...(metadata && { metadata }),
    };

    // 添加到索引
    this.messageIndex.set(messageId, message);

    // 添加到历史记录
    this.addToHistory(sessionId, message);

    // 更新统计
    this.stats.totalMessages++;
    this.stats.messagesByRole[role]++;
    this.stats.messagesByStatus[message.status]++;

    this.logger.debug('添加消息', {
      messageId,
      role,
      sessionId,
      contentLength: content.length,
    });

    // 发送消息添加事件
    this.eventBus.emit('message:added', {
      message,
      sessionId,
    });

    return message;
  }

  /**
   * 更新消息状态
   */
  public updateMessageStatus(messageId: string, status: MessageStatus): boolean {
    const message = this.messageIndex.get(messageId);
    if (!message) {
      this.logger.warn('消息不存在', { messageId });
      return false;
    }

    const oldStatus = message.status;
    message.status = status;
    message.updatedAt = Date.now();

    // 更新统计
    this.stats.messagesByStatus[oldStatus]--;
    this.stats.messagesByStatus[status]++;

    this.logger.debug('更新消息状态', {
      messageId,
      oldStatus,
      newStatus: status,
    });

    // 发送状态更新事件
    this.eventBus.emit('message:status-updated', {
      messageId,
      oldStatus,
      newStatus: status,
      message,
    });

    return true;
  }

  /**
   * 获取消息
   */
  public getMessage(messageId: string): ChatMessage | null {
    return this.messageIndex.get(messageId) || null;
  }

  /**
   * 获取会话消息历史
   */
  public getMessageHistory(sessionId: string): MessageHistory | null {
    return this.messageHistories.get(sessionId) || null;
  }

  /**
   * 获取会话消息列表
   */
  public getMessages(sessionId: string, limit?: number, offset?: number): ChatMessage[] {
    const history = this.messageHistories.get(sessionId);
    if (!history) {
      return [];
    }

    let messages = history.messages;

    if (offset) {
      messages = messages.slice(offset);
    }

    if (limit) {
      messages = messages.slice(0, limit);
    }

    return messages;
  }

  /**
   * 清空会话消息
   */
  public clearSession(sessionId: string): boolean {
    const history = this.messageHistories.get(sessionId);
    if (!history) {
      return false;
    }

    // 从索引中移除消息
    for (const message of history.messages) {
      this.messageIndex.delete(message.id);

      // 更新统计
      this.stats.totalMessages--;
      this.stats.messagesByRole[message.role]--;
      this.stats.messagesByStatus[message.status]--;
    }

    // 移除历史记录
    this.messageHistories.delete(sessionId);
    this.stats.totalSessions--;

    this.logger.info('清空会话消息', {
      sessionId,
      messageCount: history.messages.length,
    });

    // 发送清空事件
    this.eventBus.emit('message:session-cleared', {
      sessionId,
      messageCount: history.messages.length,
    });

    return true;
  }

  // ============================================================================
  // 内部方法
  // ============================================================================

  private addToHistory(sessionId: string, message: ChatMessage): void {
    let history = this.messageHistories.get(sessionId);

    if (!history) {
      history = {
        sessionId,
        messages: [],
        totalCount: 0,
        lastUpdatedAt: Date.now(),
      };
      this.messageHistories.set(sessionId, history);
      this.stats.totalSessions++;
    }

    // 添加消息
    history.messages.push(message);
    history.totalCount++;
    history.lastUpdatedAt = Date.now();

    // 检查历史记录大小限制
    if (history.messages.length > this.config.maxHistorySize) {
      const removedMessage = history.messages.shift();
      if (removedMessage) {
        this.messageIndex.delete(removedMessage.id);
        this.stats.totalMessages--;
        this.stats.messagesByRole[removedMessage.role]--;
        this.stats.messagesByStatus[removedMessage.status]--;
      }
    }
  }

  // ============================================================================
  // 事件监听
  // ============================================================================

  private setupEventListeners(): void {
    // 监听用户输入事件
    this.eventBus.on('asr:user-input', (data: unknown) => {
      const userInput = data as { userInput: string; sessionId: string; requestId: string };
      this.addMessage('user', userInput.userInput, userInput.sessionId, {
        source: 'asr',
        requestId: userInput.requestId,
      });
    });

    // 监听AI响应事件
    this.eventBus.on('ai:chat-stream-response', (data: unknown) => {
      const aiResponse = data as { message: string; sessionId: string; requestId: string };
      this.addMessage('assistant', aiResponse.message, aiResponse.sessionId, {
        source: 'ai',
        requestId: aiResponse.requestId,
        streaming: true,
      });
    });

    // 监听会话清理事件
    this.eventBus.on('session:ended', (data: unknown) => {
      const sessionData = data as { sessionId: string };
      this.clearSession(sessionData.sessionId);
    });
  }

  // ============================================================================
  // 统计和监控
  // ============================================================================

  /**
   * 获取统计信息
   */
  public getStats() {
    return {
      ...this.stats,
      activeSessions: this.messageHistories.size,
      totalMessagesInIndex: this.messageIndex.size,
    };
  }

  /**
   * 获取所有会话ID
   */
  public getAllSessionIds(): string[] {
    return Array.from(this.messageHistories.keys());
  }
}
