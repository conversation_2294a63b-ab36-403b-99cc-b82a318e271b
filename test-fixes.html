<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        #logs {
            max-height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
        }
        .log-entry.info { color: #0c5460; }
        .log-entry.warn { color: #856404; }
        .log-entry.error { color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 WebSDK 修复验证测试</h1>
        
        <div class="test-section">
            <h3>📊 SDK状态</h3>
            <div id="sdk-status" class="status info">正在初始化...</div>
            <div id="connection-status" class="status info">连接状态: 未知</div>
        </div>

        <div class="test-section">
            <h3>🧪 测试1: 连接状态指示器</h3>
            <p>验证绿点是否正确显示连接状态</p>
            <button onclick="testConnectionStatus()">测试连接状态</button>
            <div id="test1-result" class="status info">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>🧪 测试2: 录音状态查询</h3>
            <p>验证录音状态查询是否正常工作</p>
            <button onclick="testRecordingStatus()">查询录音状态</button>
            <div id="test2-result" class="status info">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>🧪 测试3: 页面切换清理</h3>
            <p>验证页面切换时的资源清理逻辑</p>
            <button onclick="testPageSwitchCleanup()">触发页面切换清理</button>
            <div id="test3-result" class="status info">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>📋 日志输出</h3>
            <button onclick="clearLogs()">清空日志</button>
            <div id="logs"></div>
        </div>
    </div>

    <!-- 引入WebSDK -->
    <script src="dist/web-service-sdk.js"></script>
    
    <script>
        let sdk = null;
        
        function addLog(message, level = 'info') {
            const logs = document.getElementById('logs');
            const entry = document.createElement('div');
            entry.className = `log-entry ${level}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logs.appendChild(entry);
            logs.scrollTop = logs.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        // 测试连接状态
        function testConnectionStatus() {
            if (!sdk) {
                updateStatus('test1-result', '❌ SDK未初始化', 'error');
                return;
            }
            
            const status = sdk.getStatus();
            addLog(`连接状态: ${JSON.stringify(status)}`, 'info');
            
            if (status.isConnected) {
                updateStatus('test1-result', '✅ 连接状态正常', 'success');
            } else {
                updateStatus('test1-result', '❌ 连接状态异常', 'error');
            }
        }

        // 测试录音状态查询
        function testRecordingStatus() {
            if (!sdk) {
                updateStatus('test2-result', '❌ SDK未初始化', 'error');
                return;
            }
            
            addLog('发送录音状态查询...', 'info');
            
            const eventBus = sdk.getEventBus();
            let hasResponse = false;
            
            const timeout = setTimeout(() => {
                if (!hasResponse) {
                    hasResponse = true;
                    updateStatus('test2-result', '⏰ 录音状态查询超时', 'error');
                    addLog('录音状态查询超时', 'error');
                }
            }, 2000);
            
            eventBus.emit('recording:status-query', {
                callback: (isRecording) => {
                    if (!hasResponse) {
                        hasResponse = true;
                        clearTimeout(timeout);
                        updateStatus('test2-result', `✅ 录音状态: ${isRecording ? '正在录音' : '未录音'}`, 'success');
                        addLog(`录音状态查询成功: ${isRecording}`, 'info');
                    }
                }
            });
        }

        // 测试页面切换清理
        function testPageSwitchCleanup() {
            if (!sdk) {
                updateStatus('test3-result', '❌ SDK未初始化', 'error');
                return;
            }
            
            addLog('触发页面切换清理事件...', 'info');
            
            const eventBus = sdk.getEventBus();
            eventBus.emit('sdk:cleanup-on-page-switch');
            
            updateStatus('test3-result', '✅ 页面切换清理事件已发送', 'success');
            addLog('页面切换清理事件已发送', 'info');
        }

        // 初始化SDK
        WebServiceSDK.init({
            hksttUrl: 'ws://localhost:8001',
            aiServerUrl: 'http://localhost:8080',
            debug: true
        }).then(sdkInstance => {
            sdk = sdkInstance;
            updateStatus('sdk-status', '✅ SDK初始化成功', 'success');
            addLog('SDK初始化成功', 'info');
            
            // 监听连接状态变化
            const eventBus = sdk.getEventBus();
            eventBus.on('connection:status', (data) => {
                const connected = data.connected;
                updateStatus('connection-status', `连接状态: ${connected ? '已连接' : '未连接'}`, connected ? 'success' : 'error');
                addLog(`连接状态变化: ${connected}`, 'info');
            });
            
            // 监听页面切换清理事件
            eventBus.on('sdk:cleanup-on-page-switch', () => {
                addLog('收到页面切换清理事件', 'info');
            });
            
            // 监听录音状态查询事件
            eventBus.on('recording:status-query', (data) => {
                addLog('收到录音状态查询事件', 'info');
            });
            
        }).catch(error => {
            updateStatus('sdk-status', `❌ SDK初始化失败: ${error.message}`, 'error');
            addLog(`SDK初始化失败: ${error.message}`, 'error');
        });
    </script>
</body>
</html>
