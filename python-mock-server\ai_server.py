#!/usr/bin/env python3
"""
AI服务器代理
支持HTTP + SSE流式响应，集成真实的chat接口并进行协议转换
"""

import asyncio
import json
import uuid
import aiohttp
import logging
from datetime import datetime
from typing import Dict, List, Optional
from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# 导入适配器
from adapters.ai_response_adapter import AIResponseAdapter


class JsonRpcRequest(BaseModel):
    """JSON-RPC 2.0 请求模型"""
    jsonrpc: str = "2.0"
    method: str
    params: Dict
    id: str
    context: Optional[Dict] = None  # 支持带context的请求


class JsonRpcResponse(BaseModel):
    """JSON-RPC 2.0 响应模型"""
    jsonrpc: str = "2.0"
    result: Optional[Dict] = None
    error: Optional[Dict] = None
    id: str


class AIServerProxy:
    """AI服务器代理类 - 集成真实chat接口"""

    def __init__(self, port: int = 8002, real_ai_url: str = "http://192.168.1.18:8001"):
        self.port = port
        self.real_ai_url = real_ai_url
        self.app = FastAPI(title="AI服务器代理", version="1.0.0")
        self.active_streams: Dict[str, bool] = {}

        # 初始化适配器
        self.logger = logging.getLogger(__name__)
        self.adapter = AIResponseAdapter(self.logger)

        # 添加CORS中间件
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],  # 允许所有来源
            allow_credentials=True,
            allow_methods=["*"],  # 允许所有HTTP方法
            allow_headers=["*"],  # 允许所有请求头
        )

        # 默认配置
        self.default_config = {
            "deviceId": "websdk-device",
            "organizationId": 999,
        }

        self.setup_routes()

    def setup_routes(self):
        """设置路由"""
        
        @self.app.get("/ping")
        async def ping():
            """健康检查接口"""
            return {
                "status": "ok",
                "service": "AI Server Proxy",
                "timestamp": datetime.now().isoformat(),
                "version": "1.0.0"
            }

        @self.app.options("/rpc")
        async def rpc_options():
            """处理JSON-RPC接口的OPTIONS预检请求"""
            return {"message": "CORS preflight OK"}



        @self.app.get("/adapter/status")
        async def adapter_status():
            """获取适配器状态"""
            status = self.adapter.get_status()
            return {
                "adapter_status": status,
                "active_streams": len(self.active_streams),
                "timestamp": datetime.now().isoformat()
            }
        
        @self.app.post("/rpc")
        async def rpc_handler(request: JsonRpcRequest):
            """JSON-RPC 2.0 统一处理接口"""
            try:
                print(f"\n📨 收到JSON-RPC请求:")
                print(f"   方法: {request.method}")
                print(f"   请求ID: {request.id}")
                print(f"   参数: {json.dumps(request.params, ensure_ascii=False, indent=2)}")
                if request.context:
                    print(f"   上下文: {json.dumps(request.context, ensure_ascii=False, indent=2)}")

                # 根据方法分发处理
                if request.method == "chat":
                    # 聊天请求返回SSE流式响应
                    return await self.handle_chat_stream(request)
                elif request.method == "speak":
                    return await self.handle_speak(request)
                elif request.method == "updateBackgroundInfo":
                    return await self.handle_update_background_info(request)
                elif request.method == "addMessages":
                    return await self.handle_add_messages(request)
                elif request.method == "pushBizData":
                    return await self.handle_push_biz_data(request)
                else:
                    # 返回JSON-RPC错误响应
                    return JsonRpcResponse(
                        id=request.id,
                        error={
                            "code": -32601,
                            "message": f"方法未找到: {request.method}"
                        }
                    ).model_dump()

            except Exception as e:
                print(f"❌ 处理JSON-RPC请求失败: {e}")
                return JsonRpcResponse(
                    id=request.id,
                    error={
                        "code": -32603,
                        "message": f"内部错误: {str(e)}"
                    }
                ).model_dump()

    async def handle_chat_stream(self, request: JsonRpcRequest):
        """处理聊天请求，返回SSE流式响应"""
        try:
            user_input = request.params.get("userInput", "")
            session_id = request.params.get("sessionId", "")

            if not user_input:
                return JsonRpcResponse(
                    id=request.id,
                    error={
                        "code": -32602,
                        "message": "缺少必需参数: userInput"
                    }
                ).model_dump()

            # 根据JSON-RPC文档：如果不传会话Id或会话id为空字符串，AI服务会返回一个新建的uuid
            if not session_id:
                session_id = str(uuid.uuid4())
                print(f"🆕 创建新会话ID: {session_id}")

            # 缓存请求参数，供SSE流式响应使用
            self.add_request_cache(request.id, user_input, session_id)

            # 在适配器中注册请求上下文
            self.adapter.register_request(request.id, session_id)

            # 标记流式响应为活跃状态
            self.active_streams[request.id] = True

            print(f"✅ 聊天请求处理成功，准备返回SSE流式响应")
            print(f"   用户输入: {user_input}")
            print(f"   会话ID: {session_id}")

            # 返回SSE流式响应
            return StreamingResponse(
                self.generate_chat_stream(request.id),
                media_type="text/plain",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Headers": "*",
                }
            )

        except Exception as e:
            print(f"❌ 处理聊天流式请求失败: {e}")
            return JsonRpcResponse(
                id=request.id,
                error={
                    "code": -32603,
                    "message": f"内部错误: {str(e)}"
                }
            ).model_dump()

    async def handle_speak(self, request: JsonRpcRequest):
        """处理语音播放请求"""
        try:
            text = request.params.get("text", "")
            delay = request.params.get("delay", 0)
            display = request.params.get("display", True)

            print(f"🔊 处理语音播放请求:")
            print(f"   文本: {text}")
            print(f"   延迟: {delay}ms")
            print(f"   显示: {display}")

            # 模拟延迟
            if delay > 0:
                await asyncio.sleep(delay / 1000)

            # 返回成功响应
            return JsonRpcResponse(
                id=request.id,
                result={
                    "success": True,
                    "message": "语音播放请求已处理",
                    "text": text,
                    "delay": delay,
                    "display": display,
                    "timestamp": datetime.now().isoformat()
                }
            ).model_dump()

        except Exception as e:
            print(f"❌ 处理语音播放请求失败: {e}")
            return JsonRpcResponse(
                id=request.id,
                error={
                    "code": -32603,
                    "message": f"内部错误: {str(e)}"
                }
            ).model_dump()

    async def handle_update_background_info(self, request: JsonRpcRequest):
        """处理背景信息更新请求"""
        try:
            session_id = request.params.get("sessionId", "")
            page = request.params.get("page", "")
            status = request.params.get("status", "")

            # 如果没有sessionId，创建一个新的
            if not session_id:
                session_id = str(uuid.uuid4())
                print(f"🆕 创建新会话ID: {session_id}")

            print(f"📝 更新背景信息:")
            print(f"   会话ID: {session_id}")
            print(f"   页面: {page}")
            print(f"   状态: {status}")

            # 返回成功响应
            return JsonRpcResponse(
                id=request.id,
                result={
                    "sessionId": session_id,
                    "success": True
                }
            ).model_dump()

        except Exception as e:
            print(f"❌ 处理背景信息更新失败: {e}")
            return JsonRpcResponse(
                id=request.id,
                error={
                    "code": -32603,
                    "message": f"内部错误: {str(e)}"
                }
            ).model_dump()

    async def handle_add_messages(self, request: JsonRpcRequest):
        """处理添加消息请求"""
        try:
            session_id = request.params.get("sessionId", "")
            messages = request.params.get("messages", [])

            # 如果没有sessionId，创建一个新的
            if not session_id:
                session_id = str(uuid.uuid4())
                print(f"🆕 创建新会话ID: {session_id}")

            print(f"💬 添加消息:")
            print(f"   会话ID: {session_id}")
            print(f"   消息数量: {len(messages)}")

            # 返回成功响应
            return JsonRpcResponse(
                id=request.id,
                result={
                    "sessionId": session_id,
                    "success": True,
                    "messageCount": len(messages)
                }
            ).model_dump()

        except Exception as e:
            print(f"❌ 处理添加消息失败: {e}")
            return JsonRpcResponse(
                id=request.id,
                error={
                    "code": -32603,
                    "message": f"内部错误: {str(e)}"
                }
            ).model_dump()

    async def handle_push_biz_data(self, request: JsonRpcRequest):
        """处理推送业务数据请求"""
        try:
            key = request.params.get("key", "")
            data = request.params.get("data")

            print(f"📊 推送业务数据:")
            print(f"   键: {key}")
            print(f"   数据: {json.dumps(data, ensure_ascii=False, indent=2) if data else 'None'}")

            # 返回成功响应
            return JsonRpcResponse(
                id=request.id,
                result={
                    "success": True,
                    "key": key
                }
            ).model_dump()

        except Exception as e:
            print(f"❌ 处理推送业务数据失败: {e}")
            return JsonRpcResponse(
                id=request.id,
                error={
                    "code": -32603,
                    "message": f"内部错误: {str(e)}"
                }
            ).model_dump()

    async def generate_chat_stream(self, requestId: str):
        """生成聊天流式响应"""
        print(f"\n🌊 开始SSE流式响应:")
        print(f"   请求ID: {requestId}")

        if requestId not in self.active_streams:
            yield f"data: {json.dumps({'error': 'Stream not found'}, ensure_ascii=False)}\n\n"
            return

        try:
            # 从缓存中获取原始请求参数
            cached_request = self.get_request_cache(requestId)
            if not cached_request:
                raise Exception(f"未找到请求缓存: {requestId}")

            user_input = cached_request["userInput"]
            session_id = cached_request["sessionId"]

            print(f"🤖 调用真实AI服务:")
            print(f"   AI服务地址: {self.real_ai_url}")
            print(f"   用户输入: {user_input}")
            print(f"   会话ID: {session_id}")

            # 构建真实AI接口的请求
            real_request = {
                "userInput": user_input,
                "deviceId": self.default_config["deviceId"],
                "organizationId": self.default_config["organizationId"],
                "sessionId": session_id if session_id else None,
                "backgroundInfo": {
                    "page": "WebSDK-Digital-Human",
                    "status": "active"
                }
            }

            print(f"📤 发送给真实AI服务的请求:")
            print(f"   {json.dumps(real_request, ensure_ascii=False, indent=2)}")

            # 调用真实AI服务
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.real_ai_url}/chat",
                    json=real_request,
                    headers={
                        "Content-Type": "application/json",
                        "Accept-Language": "zh-CN"
                    }
                ) as response:
                    if response.status != 200:
                        raise Exception(f"AI服务返回错误: {response.status}")

                    print(f"✅ 成功连接到真实AI服务，开始接收流式响应")

                    # 处理流式响应
                    buffer = ""

                    async for chunk in response.content.iter_chunked(1024):
                        if requestId not in self.active_streams:
                            break

                        chunk_text = chunk.decode('utf-8')
                        buffer += chunk_text
                        lines = buffer.split('\n')
                        buffer = lines.pop()  # 保留最后一行（可能不完整）

                        for line in lines:
                            line = line.strip()
                            if line:
                                try:
                                    # 处理SSE格式：移除 "data: " 前缀
                                    json_text = line
                                    if line.startswith('data: '):
                                        json_text = line[6:]  # 移除 "data: " 前缀
                                    elif line.startswith('data:'):
                                        json_text = line[5:]  # 移除 "data:" 前缀

                                    # 跳过空数据或非JSON数据
                                    if not json_text or json_text in ['[DONE]', '']:
                                        continue

                                    ai_data = json.loads(json_text)
                                    print(f"📨 收到AI服务响应: {ai_data}")

                                    # 使用适配器转换AI服务响应为JSON-RPC格式
                                    adapted_response = self.adapter.adapt_response(ai_data, requestId)

                                    if adapted_response:  # 只转发非None的响应（过滤中间状态）
                                        print(f"📤 转发适配后的响应: {adapted_response.get('method', adapted_response.get('id', 'unknown'))}")
                                        yield f"data: {json.dumps(adapted_response, ensure_ascii=False)}\n\n"

                                        # 如果是最终响应，结束流
                                        if 'result' in adapted_response:
                                            print(f"✅ AI对话完成")
                                            break

                                except json.JSONDecodeError as e:
                                    print(f"⚠️ 解析AI响应失败: {e}, 原始数据: {line}")
                                    continue

            print(f"✅ SSE流式响应完成: {requestId}")

        except Exception as e:
            print(f"❌ SSE流式响应错误: {e}")
            # 使用适配器创建错误响应
            context = self.adapter._get_or_create_context(requestId, "")
            error_response = self.adapter._create_error_response(str(e), context)
            yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"

        finally:
            # 清理活跃流
            if requestId in self.active_streams:
                del self.active_streams[requestId]
    
    def add_request_cache(self, request_id: str, user_input: str, session_id: str = ""):
        """缓存请求参数，用于SSE流式响应时获取"""
        # 简化实现：将请求参数存储在内存中
        if not hasattr(self, 'request_cache'):
            self.request_cache = {}

        self.request_cache[request_id] = {
            "userInput": user_input,
            "sessionId": session_id,
            "timestamp": datetime.now().isoformat()
        }

        print(f"💾 缓存请求参数: {request_id} -> {user_input}")

    def get_request_cache(self, request_id: str) -> Optional[Dict]:
        """获取缓存的请求参数"""
        if not hasattr(self, 'request_cache'):
            return None

        return self.request_cache.get(request_id)
    
    async def start_server(self):
        """启动AI服务器"""
        print(f"🤖 启动AI服务器模拟器，端口: {self.port}")
        config = uvicorn.Config(
            self.app,
            host="localhost",
            port=self.port,
            log_level="info"
        )
        server = uvicorn.Server(config)
        await server.serve()
    
    def stop(self):
        """停止服务器"""
        print("🛑 AI服务器已停止")


async def main():
    """主函数"""
    ai_server = AIServerProxy(port=8080)

    try:
        await ai_server.start_server()
    except KeyboardInterrupt:
        print("\n👋 收到中断信号，正在停止AI服务器...")
    except Exception as e:
        print(f"\n❌ AI服务器运行错误: {e}")
    finally:
        ai_server.stop()


if __name__ == "__main__":
    asyncio.run(main())
