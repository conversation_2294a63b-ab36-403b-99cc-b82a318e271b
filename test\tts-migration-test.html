<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS迁移测试 - HTTP+SSE</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        .btn-primary {
            background: #007AFF;
            color: white;
        }
        .btn-primary:hover {
            background: #0056CC;
        }
        .btn-secondary {
            background: #8E8E93;
            color: white;
        }
        .btn-secondary:hover {
            background: #6D6D70;
        }
        .btn-success {
            background: #34C759;
            color: white;
        }
        .btn-success:hover {
            background: #28A745;
        }
        .btn-warning {
            background: #FF9500;
            color: white;
        }
        .btn-warning:hover {
            background: #E6850E;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: 500;
        }
        .status.idle { background: #E5E5EA; color: #333; }
        .status.connecting { background: #FFE5B4; color: #8B4513; }
        .status.loading { background: #B4E5FF; color: #0066CC; }
        .status.playing { background: #D4EDDA; color: #155724; }
        .status.error { background: #F8D7DA; color: #721C24; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-entry.info { color: #0066CC; }
        .log-entry.warn { color: #FF9500; }
        .log-entry.error { color: #DC3545; }
        .config-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .config-section label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .config-section input, .config-section select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            resize: vertical;
            font-family: inherit;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 TTS迁移测试 - HTTP+SSE协议</h1>
        <p>测试从WebSocket到HTTP+SSE的TTS服务迁移</p>

        <!-- 配置区域 -->
        <div class="config-section">
            <h3>🔧 服务配置</h3>
            <label for="serverUrl">TTS服务器地址:</label>
            <input type="text" id="serverUrl" value="http://192.168.2.161:8080" placeholder="http://192.168.2.161:8080">
            
            <label for="protocol">协议类型:</label>
            <select id="protocol">
                <option value="http-sse" selected>HTTP+SSE (新协议)</option>
                <option value="websocket">WebSocket (旧协议)</option>
            </select>
        </div>

        <!-- 状态显示 -->
        <div class="test-section">
            <h3>📊 服务状态</h3>
            <div id="status" class="status idle">未初始化</div>
            <div class="button-group">
                <button class="btn-primary" onclick="initializeSDK()">初始化SDK</button>
                <button class="btn-secondary" onclick="destroySDK()">销毁SDK</button>
            </div>
        </div>

        <!-- 方言测试 -->
        <div class="test-section">
            <h3>🗣️ 方言支持测试</h3>
            <div class="button-group">
                <button class="btn-success" onclick="testMandarin()">普通话测试</button>
                <button class="btn-warning" onclick="testSichuan()">川渝话测试</button>
            </div>
            <textarea id="testText" rows="3" placeholder="输入要测试的文本...">收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。</textarea>
        </div>

        <!-- 播放控制 -->
        <div class="test-section">
            <h3>⏯️ 播放控制</h3>
            <div class="button-group">
                <button class="btn-primary" onclick="playCustomText()">播放自定义文本</button>
                <button class="btn-secondary" onclick="pauseTTS()">暂停</button>
                <button class="btn-secondary" onclick="resumeTTS()">继续</button>
                <button class="btn-secondary" onclick="stopTTS()">停止</button>
            </div>
        </div>

        <!-- 数字人同步测试 -->
        <div class="test-section">
            <h3>🤖 数字人同步测试</h3>
            <div id="digitalHumanStatus" class="status idle">数字人状态: 待机</div>
            <p>当TTS播放时，数字人应该自动切换到说话状态</p>
        </div>

        <!-- 错误处理测试 -->
        <div class="test-section">
            <h3>⚠️ 错误处理测试</h3>
            <div class="button-group">
                <button class="btn-warning" onclick="testNetworkError()">测试网络错误</button>
                <button class="btn-warning" onclick="testInvalidText()">测试无效文本</button>
                <button class="btn-warning" onclick="testServerError()">测试服务器错误</button>
            </div>
        </div>

        <!-- 日志区域 -->
        <div class="test-section">
            <h3>📝 测试日志</h3>
            <div class="button-group">
                <button class="btn-secondary" onclick="clearLog()">清空日志</button>
            </div>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        let sdk = null;
        let currentLanguage = 'mandarin';

        // 日志记录
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 更新状态显示
        function updateStatus(status, message) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${status}`;
            statusDiv.textContent = message;
        }

        // 更新数字人状态
        function updateDigitalHumanStatus(status) {
            const statusDiv = document.getElementById('digitalHumanStatus');
            statusDiv.className = `status ${status === 'speaking' ? 'playing' : 'idle'}`;
            statusDiv.textContent = `数字人状态: ${status === 'speaking' ? '说话中' : '待机'}`;
        }

        // 初始化SDK
        async function initializeSDK() {
            try {
                const serverUrl = document.getElementById('serverUrl').value;
                const protocol = document.getElementById('protocol').value;
                
                log(`初始化SDK - 服务器: ${serverUrl}, 协议: ${protocol}`);
                updateStatus('connecting', '正在初始化...');

                // 这里需要根据实际的SDK初始化方式调整
                // 模拟SDK初始化
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                updateStatus('idle', 'SDK已初始化');
                log('SDK初始化成功', 'info');

                // 模拟事件监听
                setupEventListeners();

            } catch (error) {
                log(`SDK初始化失败: ${error.message}`, 'error');
                updateStatus('error', 'SDK初始化失败');
            }
        }

        // 设置事件监听
        function setupEventListeners() {
            // 模拟TTS事件监听
            log('设置TTS事件监听器', 'info');
            
            // 模拟数字人同步事件
            window.addEventListener('tts-play-start', () => {
                log('TTS播放开始 - 数字人切换到说话状态', 'info');
                updateDigitalHumanStatus('speaking');
            });

            window.addEventListener('tts-play-end', () => {
                log('TTS播放结束 - 数字人切换到待机状态', 'info');
                updateDigitalHumanStatus('idle');
            });
        }

        // 销毁SDK
        function destroySDK() {
            log('销毁SDK', 'info');
            updateStatus('idle', '未初始化');
            updateDigitalHumanStatus('idle');
            sdk = null;
        }

        // 测试普通话
        async function testMandarin() {
            currentLanguage = 'mandarin';
            const text = document.getElementById('testText').value;
            await testTTS(text, '用自然清晰的普通话说话');
        }

        // 测试川渝话
        async function testSichuan() {
            currentLanguage = 'sichuan';
            const text = document.getElementById('testText').value;
            await testTTS(text, '用四川话说这句话');
        }

        // 播放自定义文本
        async function playCustomText() {
            const text = document.getElementById('testText').value;
            const instruction = currentLanguage === 'sichuan' ? '用四川话说这句话' : '用自然清晰的普通话说话';
            await testTTS(text, instruction);
        }

        // TTS测试核心函数
        async function testTTS(text, instruction) {
            if (!text.trim()) {
                log('请输入要测试的文本', 'warn');
                return;
            }

            try {
                log(`开始TTS测试 - 语言: ${currentLanguage}, 文本: ${text.substring(0, 30)}...`, 'info');
                updateStatus('connecting', '正在连接TTS服务...');

                // 模拟HTTP+SSE请求
                await simulateHTTPSSERequest(text, instruction);

            } catch (error) {
                log(`TTS测试失败: ${error.message}`, 'error');
                updateStatus('error', 'TTS测试失败');
            }
        }

        // 模拟HTTP+SSE请求
        async function simulateHTTPSSERequest(text, instruction) {
            const serverUrl = document.getElementById('serverUrl').value;
            
            updateStatus('loading', '正在合成音频...');
            
            // 模拟请求过程
            log(`发送HTTP POST请求到: ${serverUrl}/inference_instruct2_sse`, 'info');
            log(`请求参数: tts_text="${text.substring(0, 50)}...", instruct_text="${instruction}", seed=42`, 'info');
            
            // 模拟SSE事件接收
            await new Promise(resolve => {
                let chunkCount = 0;
                const maxChunks = 5;
                
                const interval = setInterval(() => {
                    chunkCount++;
                    
                    if (chunkCount === 1) {
                        log('收到SSE事件: start - 开始合成音频', 'info');
                        updateStatus('loading', '正在接收音频流...');
                        // 触发播放开始事件
                        window.dispatchEvent(new CustomEvent('tts-play-start'));
                        updateStatus('playing', '正在播放音频');
                    } else if (chunkCount <= maxChunks) {
                        log(`收到SSE事件: audio_chunk - 音频块 ${chunkCount}`, 'info');
                    } else {
                        log(`收到SSE事件: end - 合成完成，总计 ${maxChunks} 个音频块`, 'info');
                        clearInterval(interval);
                        
                        // 模拟播放完成
                        setTimeout(() => {
                            window.dispatchEvent(new CustomEvent('tts-play-end'));
                            updateStatus('idle', '播放完成');
                            resolve();
                        }, 2000);
                    }
                }, 500);
            });
        }

        // 播放控制函数
        function pauseTTS() {
            log('暂停TTS播放', 'info');
            updateStatus('paused', '已暂停');
        }

        function resumeTTS() {
            log('继续TTS播放', 'info');
            updateStatus('playing', '正在播放');
        }

        function stopTTS() {
            log('停止TTS播放', 'info');
            updateStatus('idle', '已停止');
            updateDigitalHumanStatus('idle');
        }

        // 错误处理测试
        function testNetworkError() {
            log('模拟网络错误测试', 'warn');
            setTimeout(() => {
                log('网络连接失败 - 正在重试...', 'error');
                updateStatus('error', '网络错误');
            }, 1000);
        }

        function testInvalidText() {
            log('测试无效文本处理', 'warn');
            testTTS('', '用普通话说话');
        }

        function testServerError() {
            log('模拟服务器错误测试', 'warn');
            setTimeout(() => {
                log('服务器返回500错误 - 正在重试...', 'error');
                updateStatus('error', '服务器错误');
            }, 1000);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('TTS迁移测试页面已加载', 'info');
            log('请点击"初始化SDK"开始测试', 'info');
        });
    </script>
</body>
</html>
