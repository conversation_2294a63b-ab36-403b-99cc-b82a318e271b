
/**
 * WebSDK ID管理功能集成测试
 * 测试用户API层面的ID管理功能
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { WebSDK, init } from '../../src/core/WebSDK';
import { testUtils } from '../setup';

// 不模拟依赖，使用真实的模拟服务器进行集成测试

describe('WebSDK ID管理功能', () => {
  let sdk: WebSDK;
  let mockConfig: any;

  beforeEach(async () => {
    mockConfig = {
      hksttUrl: 'ws://localhost:8001', // 使用实际的模拟服务器端口
      aiServerUrl: 'http://localhost:8002', // 使用实际的AI服务器端口
      debug: true
    };

    // 重置单例实例
    WebSDK.resetInstance();

    // 使用正确的初始化方式，连接到真实的模拟服务器
    try {
      sdk = await init(mockConfig);
      // 等待连接建立
      await testUtils.wait(1000);
    } catch (error) {
      console.warn('SDK初始化失败，可能是服务器未启动:', error);
      // 如果服务器未启动，跳过测试
      throw new Error('模拟服务器未启动，请先运行: cd python-mock-server && uv run python start-demo.py');
    }
  }, 10000); // 增加超时时间

  afterEach(() => {
    if (sdk) {
      sdk.destroy();
    }
    WebSDK.resetInstance();
    vi.clearAllMocks();
  });

  describe('基本ID管理API', () => {
    it('应该能够生成唯一的请求ID', () => {
      const id1 = sdk.generateRequestId();
      const id2 = sdk.generateRequestId();

      expect(id1).toBeDefined();
      expect(id2).toBeDefined();
      expect(id1).not.toBe(id2);
      expect(typeof id1).toBe('string');
      expect(typeof id2).toBe('string');
    });

    it('应该支持带前缀的ID生成', () => {
      const prefix = 'user-action';
      const id = sdk.generateRequestId(prefix);

      expect(id).toMatch(new RegExp(`^${prefix}_`));
    });

    it('应该能够检测ID是否存在', async () => {
      const customId = 'test-exists-123';

      // 初始状态不存在
      expect(sdk.hasRequestId(customId)).toBe(false);

      // 发送真实的请求（使用speak方法，这是服务器支持的）
      const requestPromise = sdk.sendRequest('speak', {
        text: '测试ID检测功能'
      }, { id: customId });

      expect(sdk.hasRequestId(customId)).toBe(true);

      // 等待真实的服务器响应
      await requestPromise;
      expect(sdk.hasRequestId(customId)).toBe(false);
    }, 5000);

    it('应该能够获取pending请求信息', () => {
      // 发送几个请求
      sdk.sendRequest('method1', {}, { id: 'req1' });
      sdk.sendRequest('method2', {}, { id: 'req2' });

      const pendingInfo = sdk.getPendingRequestsInfo();

      expect(pendingInfo).toHaveLength(2);
      expect(pendingInfo.map(req => req.id)).toContain('req1');
      expect(pendingInfo.map(req => req.id)).toContain('req2');

      pendingInfo.forEach(req => {
        expect(req).toMatchObject({
          id: expect.any(String),
          method: expect.any(String),
          type: 'user',
          startTime: expect.any(Number)
        });
      });
    });
  });

  describe('请求生命周期管理', () => {
    it('应该能够清空所有pending请求', async () => {
      // 发送多个真实请求
      const promises = [
        sdk.sendRequest('speak', { text: '测试消息1' }),
        sdk.sendRequest('speak', { text: '测试消息2' }),
        sdk.sendRequest('updateBackgroundInfo', { userInfo: { name: '测试用户' } })
      ];

      // 验证有pending请求
      expect(sdk.getPendingRequestsInfo().length).toBe(3);

      // 清空所有请求
      sdk.clearPendingRequests();

      // 验证pending请求被清空
      expect(sdk.getPendingRequestsInfo().length).toBe(0);

      // 验证所有Promise都被拒绝
      await Promise.allSettled(promises).then(results => {
        results.forEach(result => {
          expect(result.status).toBe('rejected');
          if (result.status === 'rejected') {
            expect(result.reason.message).toContain('请求已取消');
          }
        });
      });
    }, 5000);

    it('应该在页面卸载时自动清空请求', () => {
      // 发送一些请求
      sdk.sendRequest('method1', {});
      sdk.sendRequest('method2', {});

      expect(sdk.getPendingRequestsInfo().length).toBe(2);

      // 模拟页面卸载事件
      window.dispatchEvent(new Event('beforeunload'));

      // 验证请求被清空
      expect(sdk.getPendingRequestsInfo().length).toBe(0);
    });
  });

  describe('自定义ID使用场景', () => {
    it('应该支持防重复提交场景', async () => {
      const formId = 'form-submit-123';

      // 第一次提交（使用真实的API）
      const promise1 = sdk.sendRequest('speak', { text: '表单提交测试' }, { id: formId });

      // 尝试重复提交（应该失败）
      await expect(
        sdk.sendRequest('speak', { text: '重复提交测试' }, { id: formId })
      ).rejects.toThrow('请求ID冲突');

      // 等待第一次提交完成
      await promise1;

      // 第一次提交完成后，可以再次使用相同ID提交
      const promise2 = sdk.sendRequest('speak', { text: '第二次提交测试' }, { id: formId });

      await expect(promise2).resolves.toBeDefined();
    }, 10000);

    it('应该支持批量请求管理', async () => {
      const batchTasks = [
        { id: 'batch-1', text: '批量消息A' },
        { id: 'batch-2', text: '批量消息B' },
        { id: 'batch-3', text: '批量消息C' }
      ];

      // 发送批量请求（使用真实的speak API）
      const promises = batchTasks.map(task =>
        sdk.sendRequest('speak', { text: task.text }, { id: task.id })
      );

      // 验证所有请求都在pending中
      const pendingInfo = sdk.getPendingRequestsInfo();
      expect(pendingInfo.length).toBe(3);

      batchTasks.forEach(task => {
        expect(pendingInfo.find(req => req.id === task.id)).toBeDefined();
      });

      // 等待所有请求完成
      const results = await Promise.all(promises);

      // 验证结果
      expect(results).toHaveLength(3);
      results.forEach(result => {
        expect(result).toBeDefined();
      });

      // 验证pending请求被清空
      expect(sdk.getPendingRequestsInfo().length).toBe(0);
    }, 15000);

    it('应该支持调试ID生成', () => {
      const debugId1 = sdk.generateRequestId('debug-login');
      const debugId2 = sdk.generateRequestId('debug-payment');

      expect(debugId1).toMatch(/^debug-login_/);
      expect(debugId2).toMatch(/^debug-payment_/);
      expect(debugId1).not.toBe(debugId2);
    });
  });

  describe('链式响应ID管理', () => {
    it('应该支持链式响应的基本功能', async () => {
      // 测试链式响应的基本ID管理功能
      // 注意：真实的链式响应需要特定的AI服务器响应格式
      // 这里主要测试ID管理的基础设施是否正常工作

      const requestPromise = sdk.sendRequest('speak', {
        text: '测试链式响应基础功能'
      });

      const result = await requestPromise;
      expect(result).toBeDefined();

      // 验证请求完成后pending列表为空
      expect(sdk.getPendingRequestsInfo().length).toBe(0);
    }, 5000);

    it('应该在清空请求时正确处理所有类型的请求', async () => {
      // 发送一个普通请求
      const promise = sdk.sendRequest('speak', { text: '测试清空功能' });

      // 验证有pending请求
      expect(sdk.getPendingRequestsInfo().length).toBe(1);

      // 清空所有请求
      sdk.clearPendingRequests();

      // 验证请求被清空
      expect(sdk.getPendingRequestsInfo().length).toBe(0);

      // 验证Promise被拒绝
      await expect(promise).rejects.toThrow('请求已取消');
    }, 5000);
  });

  describe('错误场景处理', () => {
    it('应该在SDK未就绪时抛出错误', () => {
      // 重置实例，创建一个未初始化的状态
      WebSDK.resetInstance();

      // 获取实例但不调用init
      const uninitializedSDK = WebSDK.getInstance(mockConfig);

      expect(() => uninitializedSDK.generateRequestId()).toThrow('SDK未就绪');
      expect(uninitializedSDK.hasRequestId('test')).toBe(false);
      expect(uninitializedSDK.getPendingRequestsInfo()).toEqual([]);
    });

    it('应该处理ID生成失败的情况', () => {
      // 模拟极端情况：大量pending请求导致ID生成困难
      // 这个测试主要验证错误处理逻辑

      // 发送大量请求填满pending列表
      for (let i = 0; i < 50; i++) {
        sdk.sendRequest('test', {}, { id: `test-${i}` });
      }

      // 正常情况下应该仍能生成唯一ID
      const newId = sdk.generateRequestId();
      expect(newId).toBeDefined();
      expect(typeof newId).toBe('string');
    });
  });
});
