# 极简TTS配置指南

## 概述

现在您只需要在SDK初始化时添加一个`ttsUrl`参数，就可以自动启用完整的TTS（文本转语音）功能，包括：

- ✅ 流式音频播放
- ✅ 与数字人动画精确同步
- ✅ 自动重连和错误处理
- ✅ 完全不影响其他SDK功能

## 极简使用方式

### 启用TTS功能

```javascript
// 只需添加 ttsUrl 参数即可启用TTS
WebServiceSDK.init({
    hksttUrl: 'ws://localhost:8001',           // HKSTT服务地址
    aiServerUrl: 'http://localhost:8002',      // AI服务地址
    ttsUrl: 'ws://*************:8000/ws/tts/', // TTS服务地址（新增）
    debug: true                                // 开启调试模式
}).then(sdk => {
    console.log('SDK初始化完成，TTS功能已自动启用');
    
    // 🎉 现在AI响应完成后会自动播放语音
    // 🎉 数字人会自动与语音播放同步
});
```

### 不启用TTS功能（向后兼容）

```javascript
// 不提供 ttsUrl，保持原有功能不变
WebServiceSDK.init({
    hksttUrl: 'ws://localhost:8001',    // HKSTT服务地址
    aiServerUrl: 'http://localhost:8002', // AI服务地址
    debug: true                          // 开启调试模式
}).then(sdk => {
    console.log('SDK初始化完成，未启用TTS功能');
    
    // 所有其他功能正常工作：HKSTT、AI对话、数字人等
});
```

## 工作原理

当您提供`ttsUrl`时，SDK会自动：

1. **初始化TTS服务**：创建WebSocket连接到TTS服务器
2. **监听AI响应**：当AI回复完成时，自动发送到TTS服务进行语音合成
3. **流式播放**：边接收音频数据边播放，无需等待完整下载
4. **同步数字人**：
   - TTS开始播放 → 数字人切换到说话状态
   - TTS播放结束 → 数字人切换到待机状态

## 配置参数

### 必需参数

| 参数 | 类型 | 说明 |
|------|------|------|
| `hksttUrl` | string | HKSTT服务器WebSocket地址 |
| `aiServerUrl` | string | AI服务器HTTP地址 |

### 可选参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `ttsUrl` | string | undefined | TTS服务器WebSocket地址，提供则启用TTS |
| `debug` | boolean | false | 是否开启调试模式 |
| `staticBasePath` | string | 自动检测 | 静态资源基础路径 |

## TTS服务器配置

TTS服务器需要支持CosyVoice WebSocket API协议：

- **URL格式**：`ws://[服务器地址]:[端口]/ws/tts/`
- **协议**：CosyVoice WebSocket API
- **音频格式**：WAV（Base64编码）
- **采样率**：24000Hz

## 示例代码

### HTML页面示例

```html
<!DOCTYPE html>
<html>
<head>
    <title>TTS示例</title>
</head>
<body>
    <!-- 加载SDK -->
    <script src="./dist/web-service-sdk.js"></script>
    
    <script>
        // 启用TTS的完整示例
        WebServiceSDK.init({
            hksttUrl: 'ws://localhost:8001',
            aiServerUrl: 'http://localhost:8002',
            ttsUrl: 'ws://*************:8000/ws/tts/', // 启用TTS
            debug: true
        }).then(sdk => {
            console.log('SDK就绪，TTS功能已启用');
            
            // 监听新用户事件
            sdk.onNotification('notifications/newUser', (params) => {
                console.log('检测到新用户:', params);
                sdk.showGreetingPage();
            });
        });
    </script>
</body>
</html>
```

### React组件示例

```jsx
import { useEffect } from 'react';

function App() {
    useEffect(() => {
        // 在React应用中使用
        window.WebServiceSDK.init({
            hksttUrl: 'ws://localhost:8001',
            aiServerUrl: 'http://localhost:8002',
            ttsUrl: 'ws://*************:8000/ws/tts/', // 启用TTS
            debug: process.env.NODE_ENV === 'development'
        }).then(sdk => {
            console.log('SDK初始化完成');
        });
    }, []);

    return <div>我的应用</div>;
}
```

## 手动控制TTS（高级用法）

如果需要手动控制TTS播放：

```javascript
WebServiceSDK.init({
    hksttUrl: 'ws://localhost:8001',
    aiServerUrl: 'http://localhost:8002',
    ttsUrl: 'ws://*************:8000/ws/tts/',
    debug: true
}).then(sdk => {
    // 停止TTS播放
    sdk.stopTTS();
    
    // 暂停TTS播放
    sdk.pauseTTS();
    
    // 继续TTS播放
    sdk.resumeTTS();
    
    // 获取服务状态
    const status = sdk.getServiceStatus();
    console.log('TTS状态:', status.tts);
});
```

## 错误处理

```javascript
WebServiceSDK.init({
    hksttUrl: 'ws://localhost:8001',
    aiServerUrl: 'http://localhost:8002',
    ttsUrl: 'ws://invalid-url', // 无效的TTS地址
    debug: true
}).then(sdk => {
    console.log('SDK初始化成功');
}).catch(error => {
    console.error('SDK初始化失败:', error);
    
    // 即使TTS配置错误，其他功能仍然可以正常工作
    // 可以考虑不启用TTS继续使用
});
```

## 注意事项

1. **向后兼容**：不提供`ttsUrl`时，SDK功能完全不受影响
2. **网络要求**：TTS需要稳定的WebSocket连接
3. **浏览器支持**：需要支持Web Audio API的现代浏览器
4. **性能影响**：TTS功能对性能影响很小，主要是网络带宽消耗

## 故障排除

### TTS无法播放

1. 检查TTS服务器是否运行
2. 检查WebSocket URL是否正确
3. 检查浏览器控制台是否有错误信息
4. 确认浏览器支持音频播放

### 数字人不同步

1. 确认数字人组件已正确加载
2. 检查事件总线是否正常工作
3. 查看调试日志中的同步信息

### 配置错误

1. 检查URL格式是否正确
2. 确认所有必需参数都已提供
3. 查看控制台错误信息

## 总结

通过这种极简配置方式，您只需要：

1. **添加一个参数**：在现有配置中加入`ttsUrl`
2. **零代码修改**：不需要修改任何业务逻辑
3. **自动工作**：TTS和数字人同步完全自动化

这样既保持了SDK的易用性，又提供了强大的TTS功能。
