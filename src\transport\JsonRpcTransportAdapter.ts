/**
 * JSON-RPC传输适配器
 * 为各种传输层提供统一的JSON-RPC请求-响应支持
 */

import { EventBus } from '../core/EventBus';
import {
  JsonRpcMessage,
  JsonRpcRequest,
  JsonRpcMessageHandler,
} from '../jsonrpc/JsonRpcMessageHandler';
import { Logger } from '../utils/Logger';

import { HttpTransport } from './HttpTransport';
import { SimpleWebSocketTransport } from './SimpleWebSocketTransport';
import { SSETransport } from './SSETransport';

/**
 * 传输类型
 */
export type TransportType = 'websocket' | 'http' | 'sse';

/**
 * 传输适配器配置
 */
export interface TransportAdapterConfig {
  /** 默认传输类型 */
  defaultTransport: TransportType;
  /** 方法路由配置 */
  methodRouting?: {
    [method: string]: TransportType;
  };
  /** 调试模式 */
  debug?: boolean;
}

/**
 * JSON-RPC传输适配器
 * 统一管理不同传输层的JSON-RPC通信
 */
export class JsonRpcTransportAdapter {
  private config: TransportAdapterConfig;
  private eventBus: EventBus;
  private logger: Logger;
  private messageHandler: JsonRpcMessageHandler;

  // 传输层实例
  private websocketTransport: SimpleWebSocketTransport | undefined;
  private httpTransport: HttpTransport | undefined;
  private sseTransport: SSETransport | undefined;

  constructor(
    config: TransportAdapterConfig,
    eventBus: EventBus,
    transports: {
      websocket?: SimpleWebSocketTransport;
      http?: HttpTransport;
      sse?: SSETransport;
    }
  ) {
    this.config = config;
    this.eventBus = eventBus;
    this.logger = Logger.getInstance({ prefix: 'JsonRpcTransportAdapter' });
    this.messageHandler = new JsonRpcMessageHandler();

    // 设置传输层实例
    this.websocketTransport = transports.websocket;
    this.httpTransport = transports.http;
    this.sseTransport = transports.sse;

    this.setupEventListeners();
  }

  /**
   * 发送JSON-RPC请求
   */
  public async sendRequest(request: JsonRpcRequest): Promise<void> {
    const transportType = this.getTransportForMethod(request.method);
    const transport = this.getTransport(transportType);

    if (!transport) {
      throw new Error(`传输层不可用: ${transportType}`);
    }

    const message = this.messageHandler.stringify(request);

    this.logger.info('📤 通过传输层发送JSON-RPC请求', {
      transport: transportType,
      method: request.method,
      id: request.id,
    });

    try {
      switch (transportType) {
        case 'websocket':
          await this.sendViaWebSocket(message);
          break;
        case 'http':
          await this.sendViaHttp(request);
          break;
        case 'sse':
          throw new Error('SSE传输层不支持发送请求');
        default:
          throw new Error(`不支持的传输类型: ${transportType}`);
      }
    } catch (error) {
      this.logger.error('发送JSON-RPC请求失败', {
        transport: transportType,
        method: request.method,
        id: request.id,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * 发送JSON-RPC通知
   */
  public async sendNotification(method: string, params?: any): Promise<void> {
    const notification = this.messageHandler.createNotification(method, params);
    const transportType = this.getTransportForMethod(method);
    const transport = this.getTransport(transportType);

    if (!transport) {
      throw new Error(`传输层不可用: ${transportType}`);
    }

    const message = this.messageHandler.stringify(notification);

    this.logger.info('📤 发送JSON-RPC通知', {
      transport: transportType,
      method,
    });

    try {
      switch (transportType) {
        case 'websocket':
          await this.sendViaWebSocket(message);
          break;
        case 'http':
          await this.sendViaHttp(notification);
          break;
        case 'sse':
          throw new Error('SSE传输层不支持发送通知');
        default:
          throw new Error(`不支持的传输类型: ${transportType}`);
      }
    } catch (error) {
      this.logger.error('发送JSON-RPC通知失败', {
        transport: transportType,
        method,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听请求发送事件
    this.eventBus.on('jsonrpc:send-request', (data: unknown) => {
      this.handleSendRequest(data as JsonRpcRequest);
    });
    this.eventBus.on('jsonrpc:send-notification', (data: unknown) => {
      this.handleSendNotification(data as { method: string; params?: any });
    });

    // 监听各传输层的消息事件
    this.eventBus.on('message', (data: unknown) => {
      this.handleWebSocketMessage(data as { data: string });
    });
    this.eventBus.on('http:response', (data: unknown) => {
      this.handleHttpResponse(data as { data: any; status: number });
    });
    this.eventBus.on('sse:message', (data: unknown) => {
      this.handleSSEMessage(data as { data: string; type: string });
    });
  }

  /**
   * 处理发送请求事件
   */
  private async handleSendRequest(request: JsonRpcRequest): Promise<void> {
    try {
      await this.sendRequest(request);
    } catch (error) {
      this.logger.error('处理发送请求事件失败', error);

      // 发送错误响应
      const errorResponse = this.messageHandler.createError(
        -32603,
        `传输层错误: ${(error as Error).message}`,
        request.id
      );
      this.eventBus.emit('jsonrpc:error-response', errorResponse);
    }
  }

  /**
   * 处理发送通知事件
   */
  private async handleSendNotification(data: { method: string; params?: any }): Promise<void> {
    try {
      await this.sendNotification(data.method, data.params);
    } catch (error) {
      this.logger.error('处理发送通知事件失败', error);
    }
  }

  /**
   * 处理WebSocket消息
   */
  private handleWebSocketMessage(event: { data: string }): void {
    this.processIncomingMessage(event.data, 'websocket');
  }

  /**
   * 处理HTTP响应
   */
  private handleHttpResponse(event: { data: any; status: number }): void {
    if (event.status >= 200 && event.status < 300) {
      const message = typeof event.data === 'string' ? event.data : JSON.stringify(event.data);
      this.processIncomingMessage(message, 'http');
    } else {
      this.logger.error('HTTP响应错误', { status: event.status, data: event.data });
    }
  }

  /**
   * 处理SSE消息
   */
  private handleSSEMessage(event: { data: string; type: string }): void {
    this.processIncomingMessage(event.data, 'sse');
  }

  /**
   * 处理传入的消息
   */
  private processIncomingMessage(data: string, transport: TransportType): void {
    try {
      const message = this.messageHandler.parseMessage(data);
      if (!message) {
        this.logger.warn('收到无效的JSON-RPC消息', { data, transport });
        return;
      }

      this.logger.info('📥 收到JSON-RPC消息', {
        transport,
        type: this.getMessageType(message),
        id: 'id' in message ? message.id : undefined,
        method: 'method' in message ? message.method : undefined,
      });

      // 根据消息类型分发事件
      if (this.messageHandler.isResponse(message)) {
        this.eventBus.emit('jsonrpc:response', message);
      } else if (this.messageHandler.isErrorResponse(message)) {
        this.eventBus.emit('jsonrpc:error-response', message);
      } else if (this.messageHandler.isNotification(message)) {
        this.eventBus.emit('jsonrpc:notification', message);
      } else if (this.messageHandler.isRequest(message)) {
        this.eventBus.emit('jsonrpc:request', message);
      }
    } catch (error) {
      this.logger.error('处理传入消息失败', { error, data, transport });
    }
  }

  /**
   * 通过WebSocket发送消息
   */
  private async sendViaWebSocket(message: string): Promise<void> {
    if (!this.websocketTransport) {
      throw new Error('WebSocket传输层未初始化');
    }

    if (!this.websocketTransport.isConnected()) {
      throw new Error('WebSocket未连接');
    }

    this.websocketTransport.send(message);
  }

  /**
   * 通过HTTP发送消息
   */
  private async sendViaHttp(message: JsonRpcRequest | JsonRpcMessage): Promise<void> {
    if (!this.httpTransport) {
      throw new Error('HTTP传输层未初始化');
    }

    // 根据方法名确定端点
    const endpoint = this.getHttpEndpoint(message);

    await this.httpTransport.post(endpoint, message, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  /**
   * 获取方法对应的传输类型
   */
  private getTransportForMethod(method: string): TransportType {
    if (this.config.methodRouting && this.config.methodRouting[method]) {
      return this.config.methodRouting[method];
    }
    return this.config.defaultTransport;
  }

  /**
   * 获取传输层实例
   */
  private getTransport(
    type: TransportType
  ): SimpleWebSocketTransport | HttpTransport | SSETransport | undefined {
    switch (type) {
      case 'websocket':
        return this.websocketTransport;
      case 'http':
        return this.httpTransport;
      case 'sse':
        return this.sseTransport;
      default:
        return undefined;
    }
  }

  /**
   * 获取HTTP端点
   */
  private getHttpEndpoint(message: JsonRpcRequest | JsonRpcMessage): string {
    if ('method' in message) {
      // 将方法名转换为REST风格的端点
      return `/jsonrpc/${message.method.replace(/\//g, '-')}`;
    }
    return '/jsonrpc';
  }

  /**
   * 获取消息类型描述
   */
  private getMessageType(message: JsonRpcMessage): string {
    if (this.messageHandler.isRequest(message)) return 'request';
    if (this.messageHandler.isResponse(message)) return 'response';
    if (this.messageHandler.isErrorResponse(message)) return 'error-response';
    if (this.messageHandler.isNotification(message)) return 'notification';
    return 'unknown';
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<TransportAdapterConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.logger.info('传输适配器配置已更新', newConfig);
  }

  /**
   * 销毁适配器
   */
  public destroy(): void {
    this.logger.info('销毁JSON-RPC传输适配器');

    // 清理事件监听器
    this.eventBus.clearEvent('jsonrpc:send-request');
    this.eventBus.clearEvent('jsonrpc:send-notification');
    this.eventBus.clearEvent('websocket:message');
    this.eventBus.clearEvent('http:response');
    this.eventBus.clearEvent('sse:message');
  }
}
