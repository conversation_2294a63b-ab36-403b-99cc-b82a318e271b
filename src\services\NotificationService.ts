/**
 * 通知服务
 * 负责管理和发送各种类型的通知事件
 */

import { EventBus } from '../core/EventBus';
import { generateUUID } from '../utils/helpers';
import { Logger, LogLevel } from '../utils/Logger';

/**
 * 通知类型
 */
export type NotificationType =
  | 'user-input'
  | 'ai-response'
  | 'face-status'
  | 'session-change'
  | 'page-change'
  | 'error'
  | 'system';

/**
 * 通知优先级
 */
export type NotificationPriority = 'low' | 'normal' | 'high' | 'urgent';

/**
 * 通知消息
 */
export interface NotificationMessage {
  /** 通知ID */
  id: string;
  /** 通知类型 */
  type: NotificationType;
  /** 通知标题 */
  title: string;
  /** 通知内容 */
  content: string;
  /** 优先级 */
  priority: NotificationPriority;
  /** 创建时间 */
  createdAt: number;
  /** 元数据 */
  metadata?: Record<string, unknown>;
  /** 是否已读 */
  isRead: boolean;
  /** 过期时间 */
  expiresAt?: number;
}

/**
 * 通知配置
 */
export interface NotificationConfig {
  /** 是否启用通知 */
  enabled: boolean;
  /** 通知类型过滤 */
  typeFilter?: NotificationType[];
  /** 最小优先级 */
  minPriority?: NotificationPriority;
  /** 是否发送到客户端 */
  sendToClient?: boolean;
}

/**
 * 通知服务配置
 */
export interface NotificationServiceConfig {
  /** 最大通知历史数量 */
  maxHistorySize?: number;
  /** 默认过期时间（毫秒） */
  defaultExpireTime?: number;
  /** 是否启用持久化 */
  enablePersistence?: boolean;
  /** 客户端通知配置 */
  clientConfig?: NotificationConfig;
}

/**
 * 通知服务
 */
export class NotificationService {
  private eventBus: EventBus;
  private logger: Logger;
  private config: Required<Omit<NotificationServiceConfig, 'clientConfig'>> &
    Pick<NotificationServiceConfig, 'clientConfig'>;

  // 通知存储
  private notifications: Map<string, NotificationMessage> = new Map();
  private notificationHistory: NotificationMessage[] = [];

  // 统计信息
  private stats = {
    totalNotifications: 0,
    notificationsByType: {} as Record<NotificationType, number>,
    notificationsByPriority: {} as Record<NotificationPriority, number>,
    unreadCount: 0,
  };

  constructor(eventBus: EventBus, config: NotificationServiceConfig = {}) {
    this.eventBus = eventBus;
    this.logger = Logger.getInstance({
      level: LogLevel.INFO,
      prefix: 'NotificationService',
    });

    // 合并配置
    this.config = {
      maxHistorySize: 1000,
      defaultExpireTime: 24 * 60 * 60 * 1000, // 24小时
      enablePersistence: false,
      ...config,
    };

    this.setupEventListeners();
  }

  // ============================================================================
  // 通知管理
  // ============================================================================

  /**
   * 发送通知
   */
  public async sendNotification(
    type: NotificationType,
    title: string,
    content: string,
    options: {
      priority?: NotificationPriority;
      metadata?: Record<string, unknown>;
      expiresAt?: number;
      sendToClient?: boolean;
    } = {}
  ): Promise<string> {
    const notificationId = generateUUID();
    const now = Date.now();

    const notification: NotificationMessage = {
      id: notificationId,
      type,
      title,
      content,
      priority: options.priority || 'normal',
      createdAt: now,
      isRead: false,
      expiresAt: options.expiresAt || now + this.config.defaultExpireTime,
      ...(options.metadata && { metadata: options.metadata }),
    };

    // 添加到存储
    this.notifications.set(notificationId, notification);
    this.addToHistory(notification);

    // 更新统计
    this.stats.totalNotifications++;
    this.stats.notificationsByType[type] = (this.stats.notificationsByType[type] || 0) + 1;
    this.stats.notificationsByPriority[notification.priority] =
      (this.stats.notificationsByPriority[notification.priority] || 0) + 1;
    this.stats.unreadCount++;

    this.logger.info('发送通知', {
      id: notificationId,
      type,
      title,
      priority: notification.priority,
    });

    // 发送内部事件
    this.eventBus.emit('notification:sent', notification);

    // 发送到客户端
    if (options.sendToClient !== false && this.shouldSendToClient(notification)) {
      await this.sendToClient(notification);
    }

    return notificationId;
  }

  /**
   * 标记通知为已读
   */
  public markAsRead(notificationId: string): boolean {
    const notification = this.notifications.get(notificationId);
    if (!notification) {
      return false;
    }

    if (!notification.isRead) {
      notification.isRead = true;
      this.stats.unreadCount--;

      this.logger.debug('标记通知为已读', { notificationId });

      // 发送已读事件
      this.eventBus.emit('notification:read', notification);
    }

    return true;
  }

  /**
   * 获取通知
   */
  public getNotification(notificationId: string): NotificationMessage | null {
    return this.notifications.get(notificationId) || null;
  }

  /**
   * 获取通知列表
   */
  public getNotifications(
    options: {
      type?: NotificationType;
      priority?: NotificationPriority;
      unreadOnly?: boolean;
      limit?: number;
      offset?: number;
    } = {}
  ): NotificationMessage[] {
    let notifications = Array.from(this.notifications.values());

    // 过滤
    if (options.type) {
      notifications = notifications.filter(n => n.type === options.type);
    }

    if (options.priority) {
      notifications = notifications.filter(n => n.priority === options.priority);
    }

    if (options.unreadOnly) {
      notifications = notifications.filter(n => !n.isRead);
    }

    // 排序（按创建时间倒序）
    notifications.sort((a, b) => b.createdAt - a.createdAt);

    // 分页
    if (options.offset) {
      notifications = notifications.slice(options.offset);
    }

    if (options.limit) {
      notifications = notifications.slice(0, options.limit);
    }

    return notifications;
  }

  /**
   * 删除通知
   */
  public deleteNotification(notificationId: string): boolean {
    const notification = this.notifications.get(notificationId);
    if (!notification) {
      return false;
    }

    this.notifications.delete(notificationId);

    // 更新统计
    this.stats.totalNotifications--;
    this.stats.notificationsByType[notification.type]--;
    this.stats.notificationsByPriority[notification.priority]--;

    if (!notification.isRead) {
      this.stats.unreadCount--;
    }

    this.logger.debug('删除通知', { notificationId });

    // 发送删除事件
    this.eventBus.emit('notification:deleted', notification);

    return true;
  }

  /**
   * 清空所有通知
   */
  public clearAll(): number {
    const count = this.notifications.size;

    this.notifications.clear();
    this.notificationHistory = [];

    // 重置统计
    this.stats = {
      totalNotifications: 0,
      notificationsByType: {} as Record<NotificationType, number>,
      notificationsByPriority: {} as Record<NotificationPriority, number>,
      unreadCount: 0,
    };

    this.logger.info('清空所有通知', { count });

    // 发送清空事件
    this.eventBus.emit('notification:cleared', { count });

    return count;
  }

  // ============================================================================
  // 内部方法
  // ============================================================================

  private addToHistory(notification: NotificationMessage): void {
    this.notificationHistory.push(notification);

    // 检查历史记录大小限制
    if (this.notificationHistory.length > this.config.maxHistorySize) {
      this.notificationHistory.shift();
    }
  }

  private shouldSendToClient(notification: NotificationMessage): boolean {
    const clientConfig = this.config.clientConfig;
    if (!clientConfig || !clientConfig.enabled) {
      return false;
    }

    // 类型过滤
    if (clientConfig.typeFilter && !clientConfig.typeFilter.includes(notification.type)) {
      return false;
    }

    // 优先级过滤
    if (clientConfig.minPriority) {
      const priorityLevels: Record<NotificationPriority, number> = {
        low: 1,
        normal: 2,
        high: 3,
        urgent: 4,
      };

      const minLevel = priorityLevels[clientConfig.minPriority];
      const notificationLevel = priorityLevels[notification.priority];

      if (notificationLevel < minLevel) {
        return false;
      }
    }

    return true;
  }

  private async sendToClient(notification: NotificationMessage): Promise<void> {
    try {
      // 通过事件总线发送通知到客户端
      this.eventBus.emit('notification:send-to-client', {
        id: notification.id,
        type: notification.type,
        title: notification.title,
        content: notification.content,
        priority: notification.priority,
        createdAt: notification.createdAt,
        metadata: notification.metadata,
      });

      this.logger.debug('通知已发送到客户端', {
        id: notification.id,
        type: notification.type,
      });
    } catch (error) {
      this.logger.error('发送通知到客户端失败', {
        id: notification.id,
        error,
      });
    }
  }

  // getClientNotificationMethod方法已移除，改为通过事件总线发送

  // ============================================================================
  // 事件监听
  // ============================================================================

  private setupEventListeners(): void {
    // 监听用户输入事件
    this.eventBus.on('asr:user-input', (data: unknown) => {
      const userInput = data as { userInput: string; sessionId: string; requestId: string };
      this.sendNotification('user-input', '用户语音输入', userInput.userInput, {
        priority: 'normal',
        metadata: { sessionId: userInput.sessionId, requestId: userInput.requestId },
        sendToClient: true,
      });
    });

    // 监听AI响应事件
    this.eventBus.on('ai:chat-stream-response', (data: unknown) => {
      const aiResponse = data as { message: string; sessionId: string; requestId: string };
      this.sendNotification('ai-response', 'AI响应', aiResponse.message, {
        priority: 'normal',
        metadata: { sessionId: aiResponse.sessionId, requestId: aiResponse.requestId },
        sendToClient: true,
      });
    });

    // 监听人脸状态事件
    this.eventBus.on('hkstt:face-status', (data: unknown) => {
      const faceStatus = data as { hasFace: boolean };
      this.sendNotification(
        'face-status',
        '人脸状态变化',
        faceStatus.hasFace ? '检测到人脸' : '人脸消失',
        {
          priority: 'normal',
          metadata: { hasFace: faceStatus.hasFace },
          sendToClient: true,
        }
      );
    });

    // 监听新用户通知事件（来自ServiceCoordinator）
    this.eventBus.on('notification:send-to-client', (data: unknown) => {
      const notification = data as { method: string; params: any };

      if (notification.method === 'notifications/newUser') {
        this.logger.info('收到新用户通知事件，发送到客户端', notification.params);

        // 发送标准的JSON-RPC通知格式
        this.eventBus.emit('client:send-notification', {
          jsonrpc: '2.0',
          method: notification.method,
          params: notification.params,
        });
      }
    });

    // 监听会话变化事件
    this.eventBus.on('routing:session-change', (data: unknown) => {
      const sessionChange = data as { action: string; sessionId: string; component: string };
      this.sendNotification(
        'session-change',
        '会话状态变化',
        `会话${sessionChange.action}: ${sessionChange.sessionId}`,
        {
          priority: 'low',
          metadata: sessionChange,
          sendToClient: false, // 会话变化通常不需要发送到客户端
        }
      );
    });
  }

  // ============================================================================
  // 统计和监控
  // ============================================================================

  /**
   * 获取统计信息
   */
  public getStats() {
    return { ...this.stats };
  }

  /**
   * 清理过期通知
   */
  public cleanupExpiredNotifications(): number {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [id, notification] of this.notifications.entries()) {
      if (notification.expiresAt && now > notification.expiresAt) {
        this.deleteNotification(id);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.info('清理过期通知', { cleanedCount });
    }

    return cleanedCount;
  }

  /**
   * 获取未读通知数量
   */
  public getUnreadCount(): number {
    return this.stats.unreadCount;
  }

  /**
   * 更新客户端配置
   */
  public updateClientConfig(config: Partial<NotificationConfig>): void {
    this.config.clientConfig = {
      enabled: true,
      ...this.config.clientConfig,
      ...config,
    };

    this.logger.info('更新客户端通知配置', config);
  }
}
