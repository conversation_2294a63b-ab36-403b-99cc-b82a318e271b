# WebSDK 快速开始指南

## 概述

WebSDK是一个功能完整的数字人交互SDK，提供了与HKSTT语音识别服务、AI对话服务和TTS语音合成服务的统一接口。基于JSON-RPC 2.0协议，支持实时语音交互、智能对话和数字人动画同步。

## 核心架构

```
客户程序 → WebSDK → [HKSTT服务 + AI服务 + TTS服务]
                      ↓
                  数字人界面 + 语音交互
```

### 主要组件

- **WebSDK核心**：统一的API入口，管理所有服务连接
- **JSON-RPC管理器**：处理与各服务的通信协议
- **事件总线**：组件间的消息传递机制
- **数字人控制器**：管理数字人动画和界面
- **语音服务**：ASR识别、TTS合成、音频播放

## 极简使用方式

### 1. 基础初始化（2行代码）

```html
<!DOCTYPE html>
<html>
<head>
    <title>数字人交互系统</title>
</head>
<body>
    <!-- 加载SDK -->
    <script src="./dist/web-service-sdk.js"></script>
    
    <script>
        // 极简初始化 - 只需要服务地址
        WebServiceSDK.init({
            hksttUrl: 'ws://localhost:8001',           // 语音识别服务
            aiServerUrl: 'http://localhost:8002',      // AI对话服务
            ttsUrl: 'ws://*************:8000/ws/tts/', // 语音合成服务（可选）
            debug: true                                // 开启调试模式
        }).then(sdk => {
            console.log('🎉 数字人系统就绪！');
            
            // 系统会自动处理：
            // ✅ 语音识别 → AI对话 → 语音播放 → 数字人动画
            // ✅ 新用户检测和欢迎页面
            // ✅ 所有服务的连接和重连
        });
    </script>
</body>
</html>
```

### 2. 监听系统事件

```javascript
WebServiceSDK.init(config).then(sdk => {
    // 监听新用户进入
    sdk.onNotification('notifications/newUser', (params) => {
        console.log('检测到新用户');
        sdk.showGreetingPage(); // 显示欢迎页面
    });
    
    // 监听用户语音输入
    sdk.onNotification('notifications/userInput', (params) => {
        console.log('用户说：', params.userInput);
    });
    
    // 监听人脸状态变化
    sdk.onNotification('notifications/faceStatus', (params) => {
        console.log('人脸状态：', params.hasFace ? '有人' : '无人');
    });
});
```

## 核心功能详解

### 1. 自动语音交互流程

系统会自动处理完整的语音交互链路：

```
用户说话 → ASR识别 → AI理解 → 生成回复 → TTS播放 → 数字人动画
```

**无需编程**：系统自动处理整个流程，客户程序只需监听需要的事件。

### 2. AI智能操作处理

当AI需要执行业务操作时，会返回action指令：

```javascript
// 统一的Action监听（推荐）
sdk.onAction((result) => {
    console.log('AI要求执行操作：', result.action);
    
    switch (result.action) {
        case 'login':
            // AI要求用户登录
            showLoginDialog(result.data.type); // 'idCard', 'password'等
            break;
            
        case 'register':
            // AI要求用户注册
            showRegisterForm(result.data);
            break;
            
        case 'submitOrder':
            // AI要求提交订单
            submitOrder(result.data);
            break;
            
        case 'update':
            // AI要求更新数据
            if (result.type === 'form') {
                updateForm(result.data); // 增量更新表单
            } else if (result.type === 'filters') {
                updateFilters(result.data); // 全量更新筛选条件
            }
            break;
    }
});
```

### 3. 主动发送指令给AI

客户程序可以主动向AI发送各种指令：

```javascript
// 让数字人说话
await sdk.sendRequest('speak', {
    text: '您好，欢迎使用我们的服务',
    delay: 1000,    // 延时1秒
    display: true   // 显示在气泡中
});

// 更新背景信息（页面切换、状态变化等）
await sdk.sendRequest('updateBackgroundInfo', {
    sessionId: 'current-session',
    page: 'login-page',
    status: 'waiting-for-input'
});

// 添加对话历史
await sdk.sendRequest('addMessages', {
    sessionId: 'current-session',
    messages: [
        { role: 'user', content: '我想查询余额' },
        { role: 'assistant', content: '好的，我来帮您查询' }
    ]
});

// 推送业务数据
await sdk.sendRequest('pushBizData', {
    key: 'news',
    data: {
        title: '今日新闻',
        content: '...'
    }
});
```

## 实际应用场景

### 场景1：银行营业厅数字人

```javascript
WebServiceSDK.init(config).then(sdk => {
    // 检测到新客户
    sdk.onNotification('notifications/newUser', () => {
        sdk.showGreetingPage();
        
        // 更新当前页面信息
        sdk.sendRequest('updateBackgroundInfo', {
            page: 'bank-lobby',
            status: 'new-customer'
        });
    });
    
    // 处理AI的业务操作指令
    sdk.onAction((result) => {
        switch (result.action) {
            case 'login':
                // 显示身份验证界面
                showIDCardReader();
                break;
            case 'update':
                if (result.type === 'form') {
                    // AI帮助填写表单
                    fillBankForm(result.data);
                }
                break;
        }
    });
});
```

### 场景2：智能客服系统

```javascript
WebServiceSDK.init(config).then(sdk => {
    // 监听客户问题
    sdk.onNotification('notifications/userInput', (params) => {
        console.log('客户问题：', params.userInput);
        
        // 更新客服状态
        sdk.sendRequest('updateBackgroundInfo', {
            page: 'customer-service',
            status: 'handling-inquiry'
        });
    });
    
    // 处理AI的客服操作
    sdk.onAction((result) => {
        if (result.action === 'submitOrder') {
            // AI建议的解决方案
            showSolutionDialog(result.data);
        }
    });
});
```

### 场景3：教育培训系统

```javascript
WebServiceSDK.init(config).then(sdk => {
    // 推送课程信息
    sdk.sendRequest('pushBizData', {
        key: 'course-info',
        data: {
            courseName: 'JavaScript基础',
            progress: 60,
            nextLesson: '异步编程'
        }
    });
    
    // 处理学习进度更新
    sdk.onAction((result) => {
        if (result.action === 'update' && result.type === 'progress') {
            updateLearningProgress(result.data);
        }
    });
});
```

## 高级特性

### 1. 链式操作支持

AI可以返回需要多步执行的操作：

```javascript
sdk.onAction((result) => {
    if (result.nextRequestId) {
        console.log('这是多步操作的第一步，后续还有操作');
        // SDK会自动处理后续步骤
    }
});

// 监听链式操作的后续步骤
sdk.getEventBus().on('ai:chained-response', (data) => {
    console.log('链式操作的后续步骤：', data.result);
});
```

### 2. 实时状态监听

```javascript
// 监听AI处理进度
sdk.onNotification('notifications/status', (params) => {
    console.log('AI状态：', params.message);
    showProcessingStatus(params.message);
});

// 监听流式响应（实时显示AI回复）
sdk.getEventBus().on('ai:stream:progress', (data) => {
    console.log('AI实时回复：', data.message);
    appendToChatBubble(data.message);
});
```

### 3. 错误处理

```javascript
try {
    await sdk.sendRequest('speak', { text: '测试' });
} catch (error) {
    if (error.code === -32601) {
        console.log('方法不存在');
    } else if (error.code === -32602) {
        console.log('参数错误');
    } else {
        console.log('其他错误：', error.message);
    }
}
```

## 部署配置

### 服务端要求

1. **HKSTT服务**：WebSocket端口8001，处理语音识别
2. **AI服务**：HTTP端口8002，处理对话逻辑
3. **TTS服务**：WebSocket端口8000，处理语音合成（可选）

### 客户端要求

1. **现代浏览器**：支持WebSocket、Web Audio API
2. **HTTPS环境**：生产环境需要HTTPS（麦克风权限）
3. **网络连接**：稳定的网络连接，支持WebSocket

### 配置示例

```javascript
// 开发环境
const devConfig = {
    hksttUrl: 'ws://localhost:8001',
    aiServerUrl: 'http://localhost:8002',
    ttsUrl: 'ws://localhost:8000/ws/tts/',
    debug: true
};

// 生产环境
const prodConfig = {
    hksttUrl: 'wss://voice.yourcompany.com',
    aiServerUrl: 'https://ai.yourcompany.com',
    ttsUrl: 'wss://tts.yourcompany.com/ws/tts/',
    debug: false
};
```

## 总结

WebSDK提供了完整的数字人交互解决方案：

- **极简集成**：2行代码即可启动完整系统
- **自动化处理**：语音识别→AI对话→语音播放全自动
- **智能操作**：AI可以主动要求执行各种业务操作
- **实时交互**：支持流式响应和实时状态更新
- **高度可定制**：丰富的事件系统支持各种定制需求

客户程序只需要关注业务逻辑，所有底层的语音处理、AI通信、数字人控制都由SDK自动处理。

## 完整示例代码

### React应用集成

```jsx
import React, { useEffect, useState } from 'react';

function DigitalHumanApp() {
    const [sdk, setSdk] = useState(null);
    const [userInput, setUserInput] = useState('');
    const [aiResponse, setAiResponse] = useState('');
    const [isConnected, setIsConnected] = useState(false);

    useEffect(() => {
        // 初始化SDK
        WebServiceSDK.init({
            hksttUrl: 'ws://localhost:8001',
            aiServerUrl: 'http://localhost:8002',
            ttsUrl: 'ws://*************:8000/ws/tts/',
            debug: true
        }).then(sdkInstance => {
            setSdk(sdkInstance);
            setIsConnected(true);

            // 监听用户输入
            sdkInstance.onNotification('notifications/userInput', (params) => {
                setUserInput(params.userInput);
            });

            // 监听AI操作
            sdkInstance.onAction((result) => {
                setAiResponse(result.message);
                handleAIAction(result);
            });

            // 监听新用户
            sdkInstance.onNotification('notifications/newUser', () => {
                sdkInstance.showGreetingPage();
            });
        }).catch(error => {
            console.error('SDK初始化失败:', error);
        });
    }, []);

    const handleAIAction = (result) => {
        switch (result.action) {
            case 'login':
                // 显示登录界面
                showLoginModal(result.data.type);
                break;
            case 'update':
                if (result.type === 'form') {
                    // 更新表单数据
                    updateFormData(result.data);
                }
                break;
        }
    };

    const speakText = async (text) => {
        if (sdk) {
            try {
                await sdk.sendRequest('speak', {
                    text: text,
                    display: true
                });
            } catch (error) {
                console.error('语音播放失败:', error);
            }
        }
    };

    return (
        <div className="digital-human-app">
            <div className="status">
                状态: {isConnected ? '已连接' : '连接中...'}
            </div>

            <div className="interaction">
                <div className="user-input">
                    <h3>用户输入:</h3>
                    <p>{userInput || '等待用户说话...'}</p>
                </div>

                <div className="ai-response">
                    <h3>AI回复:</h3>
                    <p>{aiResponse || '等待AI响应...'}</p>
                </div>
            </div>

            <div className="controls">
                <button onClick={() => speakText('欢迎使用数字人服务')}>
                    测试语音播放
                </button>
                <button onClick={() => sdk?.showGreetingPage()}>
                    显示欢迎页面
                </button>
            </div>
        </div>
    );
}

export default DigitalHumanApp;
```

### Vue应用集成

```vue
<template>
  <div class="digital-human-app">
    <div class="status">
      状态: {{ isConnected ? '已连接' : '连接中...' }}
    </div>

    <div class="interaction">
      <div class="user-input">
        <h3>用户输入:</h3>
        <p>{{ userInput || '等待用户说话...' }}</p>
      </div>

      <div class="ai-response">
        <h3>AI回复:</h3>
        <p>{{ aiResponse || '等待AI响应...' }}</p>
      </div>
    </div>

    <div class="controls">
      <button @click="speakText('欢迎使用数字人服务')">
        测试语音播放
      </button>
      <button @click="showGreeting">
        显示欢迎页面
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DigitalHumanApp',
  data() {
    return {
      sdk: null,
      userInput: '',
      aiResponse: '',
      isConnected: false
    };
  },

  async mounted() {
    try {
      this.sdk = await WebServiceSDK.init({
        hksttUrl: 'ws://localhost:8001',
        aiServerUrl: 'http://localhost:8002',
        ttsUrl: 'ws://*************:8000/ws/tts/',
        debug: true
      });

      this.isConnected = true;
      this.setupEventListeners();

    } catch (error) {
      console.error('SDK初始化失败:', error);
    }
  },

  methods: {
    setupEventListeners() {
      // 监听用户输入
      this.sdk.onNotification('notifications/userInput', (params) => {
        this.userInput = params.userInput;
      });

      // 监听AI操作
      this.sdk.onAction((result) => {
        this.aiResponse = result.message;
        this.handleAIAction(result);
      });

      // 监听新用户
      this.sdk.onNotification('notifications/newUser', () => {
        this.sdk.showGreetingPage();
      });
    },

    handleAIAction(result) {
      switch (result.action) {
        case 'login':
          this.showLoginModal(result.data.type);
          break;
        case 'update':
          if (result.type === 'form') {
            this.updateFormData(result.data);
          }
          break;
      }
    },

    async speakText(text) {
      if (this.sdk) {
        try {
          await this.sdk.sendRequest('speak', {
            text: text,
            display: true
          });
        } catch (error) {
          console.error('语音播放失败:', error);
        }
      }
    },

    showGreeting() {
      if (this.sdk) {
        this.sdk.showGreetingPage();
      }
    }
  }
};
</script>
```

## 常见问题解答

### Q1: 如何处理网络连接问题？

```javascript
WebServiceSDK.init(config).then(sdk => {
    // 监听连接状态
    sdk.getEventBus().on('transport:websocket:connected', () => {
        console.log('WebSocket连接成功');
    });

    sdk.getEventBus().on('transport:websocket:disconnected', () => {
        console.log('WebSocket连接断开，正在重连...');
    });

    sdk.getEventBus().on('transport:websocket:error', (error) => {
        console.error('WebSocket连接错误:', error);
    });
});
```

### Q2: 如何自定义数字人界面？

```javascript
// 隐藏默认数字人界面
WebServiceSDK.init({
    ...config,
    // 通过CSS隐藏默认界面
}).then(sdk => {
    // 使用自定义界面
    const customUI = document.getElementById('my-custom-digital-human');

    // 监听语音播放状态来控制自定义动画
    sdk.getEventBus().on('tts:play-start', () => {
        customUI.classList.add('speaking');
    });

    sdk.getEventBus().on('tts:play-end', () => {
        customUI.classList.remove('speaking');
    });
});
```

### Q3: 如何处理多语言支持？

```javascript
// 推送语言配置
await sdk.sendRequest('pushBizData', {
    key: 'language-config',
    data: {
        locale: 'zh-CN',
        supportedLanguages: ['zh-CN', 'en-US']
    }
});

// 更新系统语言设置
sdk.onAction((result) => {
    if (result.action === 'update' && result.type === 'settings') {
        if (result.data.locale) {
            changeSystemLanguage(result.data.locale);
        }
    }
});
```

### Q4: 如何实现自定义业务逻辑？

```javascript
sdk.onAction((result) => {
    // 处理自定义业务操作
    switch (result.action) {
        case 'customBusinessAction':
            // 执行自定义业务逻辑
            handleCustomBusiness(result.data);
            break;

        case 'queryDatabase':
            // 查询数据库
            queryBusinessData(result.data.query)
                .then(data => {
                    // 将查询结果推送给AI
                    sdk.sendRequest('pushBizData', {
                        key: 'query-result',
                        data: data
                    });
                });
            break;
    }
});
```

## 最佳实践

### 1. 错误处理和重试机制

```javascript
async function safeRequest(sdk, method, params, maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
        try {
            return await sdk.sendRequest(method, params);
        } catch (error) {
            console.warn(`请求失败，第${i + 1}次重试:`, error.message);
            if (i === maxRetries - 1) throw error;
            await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
        }
    }
}
```

### 2. 性能优化

```javascript
// 批量推送数据，避免频繁请求
const batchData = {
    userProfile: userData,
    systemConfig: configData,
    businessData: bizData
};

await sdk.sendRequest('pushBizData', {
    key: 'batch-update',
    data: batchData
});
```

### 3. 调试和监控

```javascript
WebServiceSDK.init({
    ...config,
    debug: true // 开启详细日志
}).then(sdk => {
    // 监控所有事件
    sdk.getEventBus().on('*', (eventName, data) => {
        console.log(`事件: ${eventName}`, data);
    });
});
```
