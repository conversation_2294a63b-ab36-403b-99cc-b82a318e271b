/**
 * ID管理功能基础集成测试
 * 测试核心的ID管理功能，不依赖复杂的服务器连接
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { JsonRpcRequestManager } from '../../src/jsonrpc/JsonRpcRequestManager';
import { testUtils } from '../setup';

describe('ID管理功能集成测试', () => {
  let requestManager: JsonRpcRequestManager;
  let mockEventBus: any;

  beforeEach(() => {
    mockEventBus = testUtils.createMockEventBus();
    requestManager = new JsonRpcRequestManager(mockEventBus);
  });

  afterEach(() => {
    requestManager.destroy();
    vi.clearAllMocks();
  });

  describe('完整的ID生命周期测试', () => {
    it('应该完整地处理请求ID的生命周期', async () => {
      console.log('🧪 开始测试ID生命周期...');

      // 1. 生成唯一ID
      const customId = requestManager.generateUniqueRequestId('lifecycle');
      expect(customId).toMatch(/^lifecycle_/);
      console.log(`✅ 生成唯一ID: ${customId}`);

      // 2. 检查ID不存在
      expect(requestManager.hasRequestId(customId)).toBe(false);
      console.log('✅ 确认ID初始不存在');

      // 3. 发送请求，ID应该存在
      const requestPromise = requestManager.sendRequest('speak', {
        text: '测试ID生命周期'
      }, { id: customId });

      expect(requestManager.hasRequestId(customId)).toBe(true);
      expect(requestManager.getPendingRequestCount()).toBe(1);
      console.log('✅ 发送请求后ID存在于pending列表');

      // 4. 模拟服务器响应
      setTimeout(() => {
        mockEventBus.emit('jsonrpc:response', {
          jsonrpc: '2.0',
          result: { message: '响应成功', timestamp: Date.now() },
          id: customId
        });
      }, 100);

      // 5. 等待响应完成
      const result = await requestPromise;
      expect(result).toEqual({
        message: '响应成功',
        timestamp: expect.any(Number)
      });
      console.log('✅ 收到响应，请求完成');

      // 6. 请求完成后ID应该不存在
      expect(requestManager.hasRequestId(customId)).toBe(false);
      expect(requestManager.getPendingRequestCount()).toBe(0);
      console.log('✅ 请求完成后ID从pending列表移除');

      console.log('🎉 ID生命周期测试完成！');
    });

    it('应该正确处理并发请求的ID管理', async () => {
      console.log('🧪 开始测试并发请求ID管理...');

      const requestCount = 5;
      const requests: Array<{
        id: string;
        promise: Promise<unknown>;
      }> = [];

      // 1. 发送多个并发请求
      for (let i = 0; i < requestCount; i++) {
        const id = `concurrent-${i}-${Date.now()}`;
        const promise = requestManager.sendRequest('speak', {
          text: `并发请求 ${i}`
        }, { id });

        requests.push({ id, promise });
      }

      console.log(`✅ 发送了 ${requestCount} 个并发请求`);
      expect(requestManager.getPendingRequestCount()).toBe(requestCount);

      // 2. 验证所有ID都存在
      requests.forEach(({ id }) => {
        expect(requestManager.hasRequestId(id)).toBe(true);
      });
      console.log('✅ 所有请求ID都在pending列表中');

      // 3. 模拟并发响应
      setTimeout(() => {
        requests.forEach(({ id }, index) => {
          setTimeout(() => {
            mockEventBus.emit('jsonrpc:response', {
              jsonrpc: '2.0',
              result: { index, message: `响应 ${index}` },
              id
            });
          }, index * 10); // 错开响应时间
        });
      }, 50);

      // 4. 等待所有请求完成
      const results = await Promise.all(requests.map(r => r.promise));

      expect(results).toHaveLength(requestCount);
      results.forEach((result, index) => {
        expect(result).toEqual({
          index,
          message: `响应 ${index}`
        });
      });
      console.log('✅ 所有并发请求都收到了正确的响应');

      // 5. 验证所有ID都被清理
      expect(requestManager.getPendingRequestCount()).toBe(0);
      requests.forEach(({ id }) => {
        expect(requestManager.hasRequestId(id)).toBe(false);
      });
      console.log('✅ 所有请求完成后pending列表为空');

      console.log('🎉 并发请求ID管理测试完成！');
    });

    it('应该正确处理链式响应ID管理', async () => {
      console.log('🧪 开始测试链式响应ID管理...');

      // 1. 发送初始请求
      const initialId = 'chain-initial';
      const chainedId = 'chain-step-2';

      const initialPromise = requestManager.sendRequest('speak', {
        text: '开始链式操作'
      }, { id: initialId });

      expect(requestManager.getPendingRequestCount()).toBe(1);
      console.log('✅ 发送初始请求');

      // 2. 模拟包含nextRequestId的响应
      setTimeout(() => {
        mockEventBus.emit('jsonrpc:response', {
          jsonrpc: '2.0',
          result: {
            message: '第一步完成',
            action: 'register',
            nextRequestId: chainedId
          },
          id: initialId
        });
      }, 50);

      // 3. 等待初始请求完成
      const initialResult = await initialPromise;
      expect((initialResult as any).nextRequestId).toBe(chainedId);
      console.log('✅ 初始请求完成，包含nextRequestId');

      // 4. 验证链式响应ID被自动注册
      expect(requestManager.getPendingRequestCount()).toBe(1);
      expect(requestManager.hasRequestId(chainedId)).toBe(true);

      const pendingRequests = requestManager.getPendingRequests();
      const chainedRequest = pendingRequests.find(req => req.id === chainedId);
      expect(chainedRequest).toBeDefined();
      expect(chainedRequest?.type).toBe('chained');
      expect(chainedRequest?.parentId).toBe(initialId);
      console.log('✅ 链式响应ID被自动注册到pending列表');

      // 5. 模拟链式响应完成
      setTimeout(() => {
        mockEventBus.emit('jsonrpc:response', {
          jsonrpc: '2.0',
          result: { message: '链式操作完成' },
          id: chainedId
        });
      }, 50);

      // 6. 等待链式响应处理完成
      await testUtils.waitFor(() => requestManager.getPendingRequestCount() === 0, 2000);

      expect(requestManager.getPendingRequestCount()).toBe(0);
      expect(requestManager.hasRequestId(chainedId)).toBe(false);
      console.log('✅ 链式响应处理完成，所有ID被清理');

      console.log('🎉 链式响应ID管理测试完成！');
    });

    it('应该正确处理请求取消和清空操作', async () => {
      console.log('🧪 开始测试请求取消和清空...');

      // 1. 发送多个请求
      const requests = [
        { id: 'cancel-1', promise: requestManager.sendRequest('speak', { text: '测试1' }, { id: 'cancel-1' }) },
        { id: 'cancel-2', promise: requestManager.sendRequest('speak', { text: '测试2' }, { id: 'cancel-2' }) },
        { id: 'cancel-3', promise: requestManager.sendRequest('speak', { text: '测试3' }, { id: 'cancel-3' }) }
      ];

      expect(requestManager.getPendingRequestCount()).toBe(3);
      console.log('✅ 发送了3个请求');

      // 2. 取消单个请求
      const cancelled = requestManager.cancelRequest('cancel-2');
      expect(cancelled).toBe(true);
      expect(requestManager.getPendingRequestCount()).toBe(2);
      expect(requestManager.hasRequestId('cancel-2')).toBe(false);
      console.log('✅ 成功取消单个请求');

      // 3. 验证被取消的请求Promise被拒绝
      await expect(requests[1].promise).rejects.toThrow('请求已取消');
      console.log('✅ 被取消的请求Promise正确被拒绝');

      // 4. 清空所有剩余请求
      requestManager.clearAllPendingRequests();
      expect(requestManager.getPendingRequestCount()).toBe(0);
      console.log('✅ 清空所有pending请求');

      // 5. 验证所有剩余请求都被拒绝
      await expect(requests[0].promise).rejects.toThrow('请求已取消');
      await expect(requests[2].promise).rejects.toThrow('请求已取消');
      console.log('✅ 所有剩余请求都被正确拒绝');

      console.log('🎉 请求取消和清空测试完成！');
    });
  });

  describe('错误处理和边界情况', () => {
    it('应该处理ID冲突', async () => {
      const duplicateId = 'duplicate-test';

      // 发送第一个请求
      requestManager.sendRequest('speak', { text: '第一个请求' }, { id: duplicateId });

      // 尝试发送相同ID的请求
      await expect(
        requestManager.sendRequest('speak', { text: '重复请求' }, { id: duplicateId })
      ).rejects.toThrow('请求ID冲突');

      console.log('✅ 正确检测并拒绝了ID冲突');
    });

    it('应该处理超时请求', async () => {
      const timeoutPromise = requestManager.sendRequest('speak', {
        text: '超时测试'
      }, { timeout: 100 }); // 100ms超时

      // 不发送响应，等待超时
      await expect(timeoutPromise).rejects.toThrow('请求超时');

      // 验证超时后请求被清理
      expect(requestManager.getPendingRequestCount()).toBe(0);

      console.log('✅ 正确处理了请求超时');
    });

    it('应该忽略未知ID的响应', () => {
      const initialCount = requestManager.getPendingRequestCount();

      // 发送未知ID的响应
      mockEventBus.emit('jsonrpc:response', {
        jsonrpc: '2.0',
        result: { message: '未知响应' },
        id: 'unknown-id-123'
      });

      // 验证pending计数没有变化
      expect(requestManager.getPendingRequestCount()).toBe(initialCount);

      console.log('✅ 正确忽略了未知ID的响应');
    });
  });
});
