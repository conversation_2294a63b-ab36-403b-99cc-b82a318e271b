<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON-RPC API演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .demo-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .demo-section h3 {
            margin-top: 0;
            color: #333;
        }

        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background: #0056b3;
        }

        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }

        .input-group {
            margin: 10px 0;
        }

        .input-group label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }

        .input-group input,
        .input-group textarea {
            width: 300px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }

        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>JSON-RPC API演示</h1>
        <p>这个演示展示了如何使用 <code>sendJsonRpcMessage</code> 方法，让用户完全控制JSON-RPC消息格式和请求ID。</p>

        <div id="status" class="status info">
            正在初始化SDK...
        </div>
    </div>

    <div class="container">
        <div class="demo-section">
            <h3>1. 基础语音播报 (speak)</h3>
            <div class="input-group">
                <label>文本内容:</label>
                <input type="text" id="speakText" value="您好，欢迎使用原生JSON-RPC API！" />
            </div>
            <div class="input-group">
                <label>延时(毫秒):</label>
                <input type="number" id="speakDelay" value="0" />
            </div>
            <div class="input-group">
                <label>请求ID:</label>
                <input type="text" id="speakId" value="speak-demo-001" />
            </div>
            <button onclick="sendSpeakRequest()">发送JSON-RPC消息（用户控制ID）</button>
            <button onclick="sendSpeakRequestOld()">发送JSON-RPC消息（自动生成ID）</button>
        </div>

        <div class="demo-section">
            <h3>2. 更新背景信息 (updateBackgroundInfo)</h3>
            <div class="input-group">
                <label>会话ID:</label>
                <input type="text" id="bgSessionId" value="demo-session-123" />
            </div>
            <div class="input-group">
                <label>页面:</label>
                <input type="text" id="bgPage" value="native-jsonrpc-demo" />
            </div>
            <div class="input-group">
                <label>状态:</label>
                <input type="text" id="bgStatus" value="testing-native-api" />
            </div>
            <div class="input-group">
                <label>请求ID:</label>
                <input type="text" id="bgId" value="bg-update-demo-001" />
            </div>
            <button onclick="sendBackgroundInfoRequest()">发送背景信息更新</button>
        </div>

        <div class="demo-section">
            <h3>3. 添加对话消息 (addMessages)</h3>
            <div class="input-group">
                <label>会话ID:</label>
                <input type="text" id="msgSessionId" value="demo-session-123" />
            </div>
            <div class="input-group">
                <label>用户消息:</label>
                <textarea id="userMessage" rows="2">我想了解原生JSON-RPC API的优势</textarea>
            </div>
            <div class="input-group">
                <label>AI回复:</label>
                <textarea id="aiMessage" rows="2">原生JSON-RPC API让您完全控制请求ID和消息格式，提供更好的调试和监控能力。</textarea>
            </div>
            <div class="input-group">
                <label>请求ID:</label>
                <input type="text" id="msgId" value="add-msg-demo-001" />
            </div>
            <button onclick="sendAddMessagesRequest()">添加对话消息</button>
        </div>

        <div class="demo-section">
            <h3>4. 推送业务数据 (pushBizData)</h3>
            <div class="input-group">
                <label>数据键名:</label>
                <input type="text" id="bizKey" value="userProfile" />
            </div>
            <div class="input-group">
                <label>业务数据:</label>
                <textarea id="bizData"
                    rows="3">{"userId": "demo-user-123", "name": "演示用户", "level": "VIP", "balance": 1000}</textarea>
            </div>
            <div class="input-group">
                <label>请求ID:</label>
                <input type="text" id="bizId" value="push-data-demo-001" />
            </div>
            <button onclick="sendPushBizDataRequest()">推送业务数据</button>
        </div>

        <div class="demo-section">
            <h3>5. 批量请求演示</h3>
            <button onclick="sendBatchRequests()">发送批量请求</button>
            <button onclick="showPendingRequests()">查看待处理请求</button>
            <button onclick="clearPendingRequests()">清空待处理请求</button>
        </div>
    </div>

    <div class="container">
        <h3>请求日志</h3>
        <button onclick="clearLog()">清空日志</button>
        <div id="logArea" class="log-area"></div>
    </div>

    <!-- 加载SDK -->
    <script src="../dist/web-service-sdk.js"></script>

    <script>
        let sdk = null;

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logArea = document.getElementById('logArea');
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logArea.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logArea').textContent = '';
        }

        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        // 初始化SDK
        async function initSDK() {
            try {
                log('正在初始化WebSDK...');

                sdk = await WebServiceSDK.init({
                    hksttUrl: 'ws://localhost:8001',
                    aiServerUrl: 'http://localhost:8080',
                    debug: true
                });

                updateStatus('SDK初始化成功！可以开始测试原生JSON-RPC API', 'success');
                log('SDK初始化成功', 'success');

                // 启用所有按钮
                const buttons = document.querySelectorAll('button');
                buttons.forEach(btn => btn.disabled = false);

            } catch (error) {
                updateStatus(`SDK初始化失败: ${error.message}`, 'error');
                log(`SDK初始化失败: ${error.message}`, 'error');
            }
        }

        // 1. 语音播报演示
        async function sendSpeakRequest() {
            const text = document.getElementById('speakText').value;
            const delay = parseInt(document.getElementById('speakDelay').value) || 0;
            const id = document.getElementById('speakId').value;

            const message = {
                jsonrpc: '2.0',
                method: 'speak',
                params: {
                    text: text,
                    delay: delay,
                    display: true
                },
                id: id
            };

            try {
                log(`发送JSON-RPC消息: ${JSON.stringify(message)}`);
                const response = await sdk.sendJsonRpcMessage(message);
                log(`收到响应: ${JSON.stringify(response)}`, 'success');
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
            }
        }

        async function sendSpeakRequestOld() {
            const text = document.getElementById('speakText').value;
            const delay = parseInt(document.getElementById('speakDelay').value) || 0;

            try {
                // 使用原生JSON-RPC API替代旧的便捷API
                const requestId = `speak-legacy-${Date.now()}`;
                log(`发送原生JSON-RPC请求（替代旧API）: ${JSON.stringify({
                    jsonrpc: '2.0',
                    method: 'speak',
                    params: { text, delay, display: true },
                    id: requestId
                })}`);

                const response = await sdk.sendJsonRpcMessage({
                    jsonrpc: '2.0',
                    method: 'speak',
                    params: {
                        text: text,
                        delay: delay,
                        display: true
                    },
                    id: requestId
                });
                log(`收到响应: ${JSON.stringify(response)}`, 'success');
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
            }
        }

        // 2. 背景信息更新演示
        async function sendBackgroundInfoRequest() {
            const sessionId = document.getElementById('bgSessionId').value;
            const page = document.getElementById('bgPage').value;
            const status = document.getElementById('bgStatus').value;
            const id = document.getElementById('bgId').value;

            const message = {
                jsonrpc: '2.0',
                method: 'updateBackgroundInfo',
                params: {
                    sessionId: sessionId,
                    page: page,
                    status: status
                },
                id: id
            };

            try {
                log(`发送背景信息更新: ${JSON.stringify(message)}`);
                const response = await sdk.sendJsonRpcMessage(message);
                log(`收到响应: ${JSON.stringify(response)}`, 'success');
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
            }
        }

        // 3. 添加消息演示
        async function sendAddMessagesRequest() {
            const sessionId = document.getElementById('msgSessionId').value;
            const userMessage = document.getElementById('userMessage').value;
            const aiMessage = document.getElementById('aiMessage').value;
            const id = document.getElementById('msgId').value;

            const message = {
                jsonrpc: '2.0',
                method: 'addMessages',
                params: {
                    sessionId: sessionId,
                    messages: [
                        { role: 'user', content: userMessage },
                        { role: 'assistant', content: aiMessage }
                    ]
                },
                id: id
            };

            try {
                log(`发送添加消息请求: ${JSON.stringify(message)}`);
                const response = await sdk.sendJsonRpcMessage(message);
                log(`收到响应: ${JSON.stringify(response)}`, 'success');
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
            }
        }

        // 4. 推送业务数据演示
        async function sendPushBizDataRequest() {
            const key = document.getElementById('bizKey').value;
            const dataStr = document.getElementById('bizData').value;
            const id = document.getElementById('bizId').value;

            try {
                const data = JSON.parse(dataStr);

                const message = {
                    jsonrpc: '2.0',
                    method: 'pushBizData',
                    params: {
                        key: key,
                        data: data
                    },
                    id: id
                };

                log(`发送业务数据推送: ${JSON.stringify(message)}`);
                const response = await sdk.sendJsonRpcMessage(message);
                log(`收到响应: ${JSON.stringify(response)}`, 'success');
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
            }
        }

        // 5. 批量请求演示
        async function sendBatchRequests() {
            const timestamp = Date.now();
            const sessionId = `batch-session-${timestamp}`;

            const requests = [
                {
                    jsonrpc: '2.0',
                    method: 'updateBackgroundInfo',
                    params: { sessionId, page: 'batch-demo', status: 'testing' },
                    id: `batch-bg-${timestamp}`
                },
                {
                    jsonrpc: '2.0',
                    method: 'addMessages',
                    params: {
                        sessionId,
                        messages: [{ role: 'user', content: '批量请求测试' }]
                    },
                    id: `batch-msg-${timestamp}`
                },
                {
                    jsonrpc: '2.0',
                    method: 'speak',
                    params: { text: '批量请求演示完成' },
                    id: `batch-speak-${timestamp}`
                }
            ];

            try {
                log(`发送批量请求 (${requests.length}个):`);
                requests.forEach(req => log(`  - ${req.method} (ID: ${req.id})`));

                const responses = await Promise.all(
                    requests.map(req => sdk.sendJsonRpcMessage(req))
                );

                log(`批量请求完成，收到 ${responses.length} 个响应:`, 'success');
                responses.forEach((resp, index) => {
                    log(`  响应 ${index + 1}: ${JSON.stringify(resp)}`);
                });
            } catch (error) {
                log(`批量请求失败: ${error.message}`, 'error');
            }
        }

        // 查看待处理请求
        function showPendingRequests() {
            try {
                const pending = sdk.getPendingRequestsInfo();
                log(`当前待处理请求 (${pending.length}个):`);
                if (pending.length === 0) {
                    log('  无待处理请求');
                } else {
                    pending.forEach(req => {
                        const duration = Date.now() - req.startTime;
                        log(`  - ${req.method} (ID: ${req.id}, 耗时: ${duration}ms, 类型: ${req.type})`);
                    });
                }
            } catch (error) {
                log(`获取待处理请求失败: ${error.message}`, 'error');
            }
        }

        // 清空待处理请求
        function clearPendingRequests() {
            try {
                sdk.clearPendingRequests();
                log('已清空所有待处理请求', 'success');
            } catch (error) {
                log(`清空待处理请求失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时初始化
        window.addEventListener('load', () => {
            // 初始禁用所有按钮
            const buttons = document.querySelectorAll('button');
            buttons.forEach(btn => btn.disabled = true);

            // 初始化SDK
            initSDK();
        });
    </script>
</body>

</html>