# JSON-RPC重构迁移指南

## 概述

本次重构对WebSDK的JSON-RPC实现进行了全面的模块化改造，提供了更清晰的架构和更强大的功能。重构完全保持向后兼容性，现有代码无需修改即可使用。

## 重构内容

### 1. 架构模块化

**之前**：所有JSON-RPC逻辑都在WebSDK类中，代码耦合度高，难以维护。

**现在**：拆分为独立的模块：
- `JsonRpcValidator` - JSON-RPC格式验证
- `JsonRpcRequestManager` - 请求-响应管理
- `JsonRpcActionHandler` - AI响应action处理
- `JsonRpcNotificationManager` - 通知管理

### 2. 统一的Action监听API

**之前**：需要监听多个分散的事件：
```typescript
// 旧方式 - 分散的事件监听
sdk.getEventBus().on('ai:action:login', handleLogin);
sdk.getEventBus().on('ai:action:register', handleRegister);
sdk.getEventBus().on('ai:action:update:form', handleFormUpdate);
// ... 更多事件
```

**现在**：提供统一的`onAction`方法：
```typescript
// 新方式 - 统一的Action监听
const unsubscribe = sdk.onAction((result) => {
  console.log('收到AI Action:', result);
  
  switch (result.action) {
    case 'login':
      handleLogin(result);
      break;
    case 'register':
      handleRegister(result);
      break;
    case 'update':
      handleUpdate(result);
      break;
    default:
      console.log('未知action:', result.action);
  }
});

// 取消监听
unsubscribe();
```

### 3. 完善的类型定义

新增了完整的TypeScript类型定义，包括：
- 所有文档中定义的方法参数类型
- AI响应action相关类型
- 通知参数类型
- 错误码常量

## 迁移步骤

### 步骤1：更新Action监听（推荐）

如果您使用了分散的action事件监听，建议迁移到新的统一API：

```typescript
// 旧代码
sdk.getEventBus().on('ai:action:login', (data) => {
  console.log('登录action:', data.data);
});

sdk.getEventBus().on('ai:action:update:form', (data) => {
  console.log('表单更新:', data.data);
});

// 新代码（推荐）
sdk.onAction((result) => {
  switch (result.action) {
    case 'login':
      console.log('登录action:', result.data);
      break;
    case 'update':
      if (result.type === 'form') {
        console.log('表单更新:', result.data);
      }
      break;
  }
});
```

### 步骤2：使用新的类型定义（可选）

如果您使用TypeScript，可以使用新的类型定义：

```typescript
import { 
  SpeakParams, 
  UpdateBackgroundInfoParams,
  AddMessagesParams,
  PushBizDataParams,
  ActionCallback 
} from 'web-service-api-sdk';

// 类型安全的方法调用
const speakParams: SpeakParams = {
  text: '您好，欢迎使用我们的服务',
  delay: 1000,
  display: true
};
await sdk.sendRequest('speak', speakParams);

// 类型安全的Action监听
const actionCallback: ActionCallback = (result) => {
  // result 有完整的类型提示
  console.log(result.action, result.data);
};
sdk.onAction(actionCallback);
```

### 步骤3：修正方法参数（如果使用了pushBizData）

根据json-rpc.md文档，`pushBizData`方法的参数结构有所调整：

```typescript
// 旧参数结构
await sdk.sendRequest('pushBizData', {
  type: 'userProfile',  // ❌ 错误
  data: { userId: '123' }
});

// 新参数结构
await sdk.sendRequest('pushBizData', {
  key: 'userProfile',   // ✅ 正确
  data: { userId: '123' }
});
```

## 向后兼容性

### 完全兼容的API

以下API完全保持兼容，无需修改：

```typescript
// ✅ 完全兼容 - sendRequest方法
const result = await sdk.sendRequest('speak', { text: '你好' });

// ✅ 完全兼容 - onNotification方法
const unsubscribe = sdk.onNotification('notifications/progress', (params) => {
  console.log('进度:', params);
});

// ✅ 完全兼容 - 所有现有的EventBus事件
sdk.getEventBus().on('ai:stream:progress', handleProgress);
```

### 保留的分散式事件

为了完全向后兼容，所有原有的分散式action事件仍然保留：

```typescript
// ✅ 仍然可用 - 旧的分散式事件
sdk.getEventBus().on('ai:action:login', handleLogin);
sdk.getEventBus().on('ai:action:update:form', handleFormUpdate);
// ... 其他事件
```

## 新功能特性

### 1. 增强的错误处理

```typescript
import { JsonRpcErrorCodes } from 'web-service-api-sdk';

try {
  await sdk.sendRequest('invalidMethod');
} catch (error) {
  if (error.code === JsonRpcErrorCodes.METHOD_NOT_FOUND) {
    console.log('方法不存在');
  }
}
```

### 2. 更严格的参数验证

新的验证器会检查方法参数是否符合文档规范：

```typescript
// 会自动验证参数格式
await sdk.sendRequest('speak', {
  text: '必需的文本参数',
  delay: 1000,      // 可选
  display: true     // 可选
});
```

### 3. 完整的通知类型支持

```typescript
// 支持所有文档中定义的通知类型
sdk.onNotification('notifications/progress', handleProgress);
sdk.onNotification('notifications/userInput', handleUserInput);
sdk.onNotification('notifications/faceStatus', handleFaceStatus);
// ... 更多通知类型
```

## 性能改进

- **模块化架构**：更好的代码组织和维护性
- **类型安全**：完整的TypeScript类型支持
- **内存优化**：更好的事件监听器管理
- **错误处理**：更详细的错误信息和调试支持

## 故障排除

### 问题1：Action监听器没有触发

**原因**：可能使用了旧的事件名称或参数结构不正确。

**解决方案**：
```typescript
// 检查响应是否包含action字段
sdk.onAction((result) => {
  console.log('收到action响应:', result);
  // 确保result.action存在且不为空
});
```

### 问题2：参数验证失败

**原因**：参数结构不符合文档规范。

**解决方案**：
```typescript
// 检查控制台的验证错误信息
// 确保参数结构符合json-rpc.md文档
```

### 问题3：类型错误

**原因**：使用了旧的类型定义。

**解决方案**：
```typescript
// 导入新的类型定义
import { SpeakParams, ActionCallback } from 'web-service-api-sdk';
```

## 总结

本次重构提供了：

1. **更好的架构** - 模块化设计，易于维护和扩展
2. **统一的API** - `onAction`方法简化了Action监听
3. **完整的类型支持** - 全面的TypeScript类型定义
4. **向后兼容** - 现有代码无需修改即可使用
5. **增强的功能** - 更好的错误处理和参数验证

建议在新项目中使用新的统一API，现有项目可以逐步迁移。所有改动都是增强性的，不会破坏现有功能。
