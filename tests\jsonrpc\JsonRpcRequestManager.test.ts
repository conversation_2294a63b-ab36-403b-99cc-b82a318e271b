/**
 * JsonRpcRequestManager 单元测试
 * 测试ID管理、链式响应、pending请求管理等核心功能
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { JsonRpcRequestManager } from '../../src/jsonrpc/JsonRpcRequestManager';
import { testUtils } from '../setup';

describe('JsonRpcRequestManager', () => {
  let requestManager: JsonRpcRequestManager;
  let mockEventBus: any;

  beforeEach(() => {
    // 创建模拟的EventBus
    mockEventBus = testUtils.createMockEventBus();

    // 创建JsonRpcRequestManager实例
    requestManager = new JsonRpcRequestManager(mockEventBus);
  });

  afterEach(() => {
    // 清理资源
    requestManager.destroy();
    vi.clearAllMocks();
  });

  describe('基本请求功能', () => {
    it('应该能够发送基本请求', async () => {
      const method = 'test.method';
      const params = { test: 'data' };

      // 发送请求（不等待响应）
      const requestPromise = requestManager.sendRequest(method, params);

      // 验证事件是否被发送
      expect(mockEventBus.emit).toHaveBeenCalledWith('client:send-request', expect.objectContaining({
        jsonrpc: '2.0',
        method,
        params,
        id: expect.any(String)
      }));

      // 验证pending请求被添加
      expect(requestManager.getPendingRequestCount()).toBe(1);

      // 模拟响应
      const sentRequest = mockEventBus.emit.mock.calls[0][1];
      const mockResponse = {
        jsonrpc: '2.0',
        result: { success: true },
        id: sentRequest.id
      };

      // 触发响应处理
      mockEventBus.emit('jsonrpc:response', mockResponse);

      // 等待请求完成
      const result = await requestPromise;

      // 验证结果
      expect(result).toEqual({ success: true });
      expect(requestManager.getPendingRequestCount()).toBe(0);
    });

    it('应该支持自定义请求ID', async () => {
      const customId = 'custom-test-id-123';
      const method = 'test.method';

      const requestPromise = requestManager.sendRequest(method, {}, { id: customId });

      // 验证使用了自定义ID
      expect(mockEventBus.emit).toHaveBeenCalledWith('client:send-request', expect.objectContaining({
        id: customId
      }));

      // 模拟响应
      mockEventBus.emit('jsonrpc:response', {
        jsonrpc: '2.0',
        result: { success: true },
        id: customId
      });

      const result = await requestPromise;
      expect(result).toEqual({ success: true });
    });

    it('应该检测ID冲突', async () => {
      const duplicateId = 'duplicate-id';

      // 发送第一个请求
      requestManager.sendRequest('test.method1', {}, { id: duplicateId });

      // 尝试发送具有相同ID的第二个请求
      await expect(
        requestManager.sendRequest('test.method2', {}, { id: duplicateId })
      ).rejects.toThrow('请求ID冲突');
    });

    it('应该处理请求超时', async () => {
      const method = 'test.timeout';
      const timeout = 100; // 100ms超时

      const requestPromise = requestManager.sendRequest(method, {}, { timeout });

      // 等待超时
      await expect(requestPromise).rejects.toThrow('请求超时');

      // 验证pending请求被清理
      expect(requestManager.getPendingRequestCount()).toBe(0);
    });
  });

  describe('链式响应功能', () => {
    it('应该自动处理链式响应', async () => {
      const method = 'test.chained';
      const nextRequestId = 'next-request-123';

      const requestPromise = requestManager.sendRequest(method, {});

      // 获取发送的请求ID
      const sentRequest = mockEventBus.emit.mock.calls[0][1];
      const originalId = sentRequest.id;

      // 模拟包含nextRequestId的响应
      const chainedResponse = {
        jsonrpc: '2.0',
        result: {
          message: '第一步完成',
          action: 'register',
          nextRequestId
        },
        id: originalId
      };

      mockEventBus.emit('jsonrpc:response', chainedResponse);

      // 等待原始请求完成
      const result = await requestPromise as any;
      expect(result.nextRequestId).toBe(nextRequestId);

      // 验证链式响应ID被注册
      expect(requestManager.getPendingRequestCount()).toBe(1);

      // 验证链式响应事件被发送
      expect(mockEventBus.emit).toHaveBeenCalledWith('ai:chained-request', {
        requestId: nextRequestId,
        parentId: originalId,
        timestamp: expect.any(Number)
      });

      // 模拟链式响应
      mockEventBus.emit('jsonrpc:response', {
        jsonrpc: '2.0',
        result: { message: '第二步完成' },
        id: nextRequestId
      });

      // 验证链式响应被处理
      expect(requestManager.getPendingRequestCount()).toBe(0);
    });

    it('应该支持多级链式响应', async () => {
      const method = 'test.multi-chained';

      const requestPromise = requestManager.sendRequest(method, {});
      const originalId = mockEventBus.emit.mock.calls[0][1].id;

      // 第一级链式响应
      const secondId = 'second-request';
      mockEventBus.emit('jsonrpc:response', {
        jsonrpc: '2.0',
        result: { message: '第一步', nextRequestId: secondId },
        id: originalId
      });

      await requestPromise;
      expect(requestManager.getPendingRequestCount()).toBe(1);

      // 第二级链式响应
      const thirdId = 'third-request';
      mockEventBus.emit('jsonrpc:response', {
        jsonrpc: '2.0',
        result: { message: '第二步', nextRequestId: thirdId },
        id: secondId
      });

      expect(requestManager.getPendingRequestCount()).toBe(1);

      // 最终响应（无nextRequestId）
      mockEventBus.emit('jsonrpc:response', {
        jsonrpc: '2.0',
        result: { message: '完成' },
        id: thirdId
      });

      expect(requestManager.getPendingRequestCount()).toBe(0);
    });
  });

  describe('请求管理功能', () => {
    it('应该能够取消单个请求', async () => {
      const requestPromise = requestManager.sendRequest('test.cancel', {});
      const requestId = mockEventBus.emit.mock.calls[0][1].id;

      expect(requestManager.getPendingRequestCount()).toBe(1);

      // 取消请求
      const cancelled = requestManager.cancelRequest(requestId);
      expect(cancelled).toBe(true);
      expect(requestManager.getPendingRequestCount()).toBe(0);

      // 验证Promise被拒绝
      await expect(requestPromise).rejects.toThrow('请求已取消');
    });

    it('应该能够取消请求链', async () => {
      // 发送主请求
      const mainPromise = requestManager.sendRequest('test.chain', {});
      const mainId = mockEventBus.emit.mock.calls[0][1].id;

      // 模拟链式响应
      const chainedId = 'chained-123';
      mockEventBus.emit('jsonrpc:response', {
        jsonrpc: '2.0',
        result: { message: '主请求完成', nextRequestId: chainedId },
        id: mainId
      });

      await mainPromise;
      expect(requestManager.getPendingRequestCount()).toBe(1);

      // 取消整个请求链
      const cancelledCount = requestManager.cancelRequestChain(mainId);
      expect(cancelledCount).toBe(1); // 取消了链式请求
      expect(requestManager.getPendingRequestCount()).toBe(0);
    });

    it('应该能够清空所有pending请求', async () => {
      // 发送多个请求
      const promises = [
        requestManager.sendRequest('test.method1', {}),
        requestManager.sendRequest('test.method2', {}),
        requestManager.sendRequest('test.method3', {})
      ];

      expect(requestManager.getPendingRequestCount()).toBe(3);

      // 清空所有请求
      requestManager.clearAllPendingRequests();

      expect(requestManager.getPendingRequestCount()).toBe(0);

      // 验证所有Promise都被拒绝
      await Promise.allSettled(promises).then(results => {
        results.forEach(result => {
          expect(result.status).toBe('rejected');
        });
      });
    });

    it('应该能够获取pending请求信息', () => {
      const customId = 'info-test-123';
      requestManager.sendRequest('test.info', { data: 'test' }, { id: customId });

      const pendingInfo = requestManager.getPendingRequests();

      expect(pendingInfo).toHaveLength(1);
      expect(pendingInfo[0]).toMatchObject({
        id: customId,
        method: 'test.info',
        type: 'user',
        startTime: expect.any(Number)
      });
    });
  });

  describe('ID生成功能', () => {
    it('应该生成唯一的请求ID', () => {
      const id1 = requestManager.generateUniqueRequestId();
      const id2 = requestManager.generateUniqueRequestId();

      expect(id1).not.toBe(id2);
      expect(typeof id1).toBe('string');
      expect(typeof id2).toBe('string');
    });

    it('应该支持带前缀的ID生成', () => {
      const prefix = 'test-prefix';
      const id = requestManager.generateUniqueRequestId(prefix);

      expect(id).toMatch(new RegExp(`^${prefix}_`));
    });

    it('应该检测ID是否存在', () => {
      const customId = 'exists-test-123';

      expect(requestManager.hasRequestId(customId)).toBe(false);

      requestManager.sendRequest('test.exists', {}, { id: customId });

      expect(requestManager.hasRequestId(customId)).toBe(true);
    });
  });

  describe('错误处理', () => {
    it('应该处理无效的响应', () => {
      // 发送无效响应
      mockEventBus.emit('jsonrpc:response', { invalid: 'response' });

      // 不应该抛出错误，应该被忽略
      expect(requestManager.getPendingRequestCount()).toBe(0);
    });

    it('应该处理未知ID的响应', () => {
      // 发送未知ID的响应
      mockEventBus.emit('jsonrpc:response', {
        jsonrpc: '2.0',
        result: { data: 'test' },
        id: 'unknown-id-123'
      });

      // 应该被忽略，不抛出错误
      expect(requestManager.getPendingRequestCount()).toBe(0);
    });

    it('应该处理错误响应', async () => {
      const requestPromise = requestManager.sendRequest('test.error', {});
      const requestId = mockEventBus.emit.mock.calls[0][1].id;

      // 发送错误响应
      mockEventBus.emit('jsonrpc:response', {
        jsonrpc: '2.0',
        error: {
          code: -32603,
          message: 'Internal error',
          data: { details: 'Something went wrong' }
        },
        id: requestId
      });

      // 验证Promise被拒绝，错误信息正确
      await expect(requestPromise).rejects.toMatchObject({
        message: 'JSON-RPC错误 [-32603]: Internal error',
        code: -32603,
        data: { details: 'Something went wrong' }
      });

      expect(requestManager.getPendingRequestCount()).toBe(0);
    });
  });
});
