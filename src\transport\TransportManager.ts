/**
 * 传输管理器
 * 统一管理WebSocket、HTTP和SSE传输层
 */

import { EventBus } from '../core/EventBus';
import { Logger, LogLevel } from '../utils/Logger';

import { HttpTransport, HttpTransportConfig } from './HttpTransport';
import { SimpleWebSocketTransport, SimpleWebSocketConfig } from './SimpleWebSocketTransport';
import { SSETransport, SSETransportConfig } from './SSETransport';

/**
 * 传输管理器配置
 */
export interface TransportManagerConfig {
  /** WebSocket传输配置 */
  websocket?: SimpleWebSocketConfig;
  /** HTTP传输配置 */
  http?: HttpTransportConfig;
  /** SSE传输配置 */
  sse?: SSETransportConfig;
  /** 调试模式 */
  debug?: boolean;
}

/**
 * 传输类型
 */
export enum TransportType {
  WEBSOCKET = 'websocket',
  HTTP = 'http',
  SSE = 'sse',
}

/**
 * 传输管理器事件
 */
export interface TransportManagerEvents {
  'transport-ready': { type: TransportType };
  'transport-error': { type: TransportType; error: Error };
  'all-transports-ready': Record<string, never>;
}

/**
 * 传输管理器
 */
export class TransportManager {
  private eventBus: EventBus;
  private logger: Logger;
  private config: TransportManagerConfig;

  private websocketTransport: SimpleWebSocketTransport | null = null;
  private httpTransport: HttpTransport | null = null;
  private sseTransport: SSETransport | null = null;

  private readyTransports = new Set<TransportType>();

  constructor(config: TransportManagerConfig, eventBus: EventBus) {
    this.config = config;
    this.eventBus = eventBus;
    this.logger = Logger.getInstance({
      level: config.debug ? LogLevel.DEBUG : LogLevel.INFO,
      prefix: 'TransportManager',
    });

    this.initializeTransports();
  }

  /**
   * 初始化传输层
   */
  private initializeTransports(): void {
    this.logger.info('初始化传输层');

    // 初始化WebSocket传输
    if (this.config.websocket) {
      this.websocketTransport = new SimpleWebSocketTransport(this.config.websocket, this.eventBus);
      this.bindWebSocketEvents();
    }

    // 初始化HTTP传输
    if (this.config.http) {
      this.httpTransport = new HttpTransport(this.config.http, this.eventBus);
      this.bindHttpEvents();
      // HTTP传输立即就绪
      this.markTransportReady(TransportType.HTTP);
    }

    // 初始化SSE传输
    if (this.config.sse) {
      this.sseTransport = new SSETransport(this.config.sse, this.eventBus);
      this.bindSSEEvents();
    }
  }

  /**
   * 绑定WebSocket事件
   */
  private bindWebSocketEvents(): void {
    if (!this.websocketTransport) return;

    this.eventBus.on('transport:state-change', (data: any) => {
      if (data.state === 'connected') {
        this.markTransportReady(TransportType.WEBSOCKET);
      } else if (data.state === 'failed') {
        this.emitEvent('transport-error', {
          type: TransportType.WEBSOCKET,
          error: data.error || new Error('WebSocket连接失败'),
        });
      }
    });
  }

  /**
   * 绑定HTTP事件
   */
  private bindHttpEvents(): void {
    if (!this.httpTransport) return;

    this.eventBus.on('http:error', (data: any) => {
      this.emitEvent('transport-error', {
        type: TransportType.HTTP,
        error: data.error,
      });
    });
  }

  /**
   * 绑定SSE事件
   */
  private bindSSEEvents(): void {
    if (!this.sseTransport) return;

    this.eventBus.on('sse:state-change', (data: any) => {
      if (data.state === 'connected') {
        this.markTransportReady(TransportType.SSE);
      } else if (data.state === 'failed') {
        this.emitEvent('transport-error', {
          type: TransportType.SSE,
          error: data.error || new Error('SSE连接失败'),
        });
      }
    });
  }

  /**
   * 标记传输层就绪
   */
  private markTransportReady(type: TransportType): void {
    if (!this.readyTransports.has(type)) {
      this.readyTransports.add(type);
      this.logger.info(`传输层就绪: ${type}`);
      this.emitEvent('transport-ready', { type });

      // 检查是否所有配置的传输层都已就绪
      this.checkAllTransportsReady();
    }
  }

  /**
   * 检查所有传输层是否就绪
   */
  private checkAllTransportsReady(): void {
    const configuredTransports = new Set<TransportType>();

    if (this.config.websocket) configuredTransports.add(TransportType.WEBSOCKET);
    if (this.config.http) configuredTransports.add(TransportType.HTTP);
    if (this.config.sse) configuredTransports.add(TransportType.SSE);

    const allReady = Array.from(configuredTransports).every(type => this.readyTransports.has(type));

    if (allReady) {
      this.logger.info('所有传输层已就绪');
      this.emitEvent('all-transports-ready', {});
    }
  }

  /**
   * 启动所有传输层
   */
  public async start(): Promise<void> {
    this.logger.info('启动传输管理器');

    const promises: Promise<void>[] = [];

    // 启动WebSocket传输
    if (this.websocketTransport) {
      promises.push(this.websocketTransport.connect());
    }

    // HTTP传输无需启动，已经就绪

    // SSE传输需要在具体使用时连接

    if (promises.length > 0) {
      await Promise.all(promises);
    }

    this.logger.info('传输管理器启动完成');
  }

  /**
   * 停止所有传输层
   */
  public stop(): void {
    this.logger.info('停止传输管理器');

    if (this.websocketTransport) {
      this.websocketTransport.disconnect();
    }

    if (this.sseTransport) {
      this.sseTransport.disconnect();
    }

    this.readyTransports.clear();
    this.logger.info('传输管理器已停止');
  }

  /**
   * 获取WebSocket传输
   */
  public getWebSocketTransport(): SimpleWebSocketTransport | null {
    return this.websocketTransport;
  }

  /**
   * 获取HTTP传输
   */
  public getHttpTransport(): HttpTransport | null {
    return this.httpTransport;
  }

  /**
   * 获取SSE传输
   */
  public getSSETransport(): SSETransport | null {
    return this.sseTransport;
  }

  /**
   * 检查传输层是否就绪
   */
  public isTransportReady(type: TransportType): boolean {
    return this.readyTransports.has(type);
  }

  /**
   * 检查所有传输层是否就绪
   */
  public areAllTransportsReady(): boolean {
    const configuredTransports = new Set<TransportType>();

    if (this.config.websocket) configuredTransports.add(TransportType.WEBSOCKET);
    if (this.config.http) configuredTransports.add(TransportType.HTTP);
    if (this.config.sse) configuredTransports.add(TransportType.SSE);

    return Array.from(configuredTransports).every(type => this.readyTransports.has(type));
  }

  /**
   * 发送事件到事件总线
   */
  private emitEvent<K extends keyof TransportManagerEvents>(
    event: K,
    data: TransportManagerEvents[K]
  ): void {
    this.eventBus.emit(`transport:${event}`, data);
  }

  /**
   * 更新传输配置
   */
  public updateConfig(newConfig: Partial<TransportManagerConfig>): void {
    this.config = { ...this.config, ...newConfig };

    // 更新各个传输层的配置
    if (newConfig.websocket && this.websocketTransport) {
      // WebSocket传输需要重新连接才能应用新配置
      this.logger.warn('WebSocket配置更新需要重新连接');
    }

    if (newConfig.http && this.httpTransport) {
      this.httpTransport.updateConfig(newConfig.http);
    }

    if (newConfig.sse && this.sseTransport) {
      this.sseTransport.updateConfig(newConfig.sse);
    }

    this.logger.info('传输管理器配置已更新', newConfig);
  }

  /**
   * 销毁传输管理器
   */
  public destroy(): void {
    this.stop();

    if (this.websocketTransport) {
      this.websocketTransport.destroy();
    }

    if (this.sseTransport) {
      this.sseTransport.destroy();
    }

    this.logger.info('传输管理器已销毁');
  }
}
