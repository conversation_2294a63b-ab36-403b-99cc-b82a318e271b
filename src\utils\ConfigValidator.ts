/**
 * 配置验证工具
 * 用于验证和规范化各种配置参数，防止无限重试等问题
 */

import { Logger } from './Logger';

/**
 * 重试配置接口
 */
export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  exponentialFactor: number;
  timeout: number;
}

/**
 * 配置验证器
 */
export class ConfigValidator {
  private static logger = Logger.getInstance({ prefix: 'ConfigValidator' });

  /**
   * 验证并规范化重试配置
   */
  static validateRetryConfig(config: Partial<RetryConfig>, defaults: RetryConfig): RetryConfig {
    const validated: RetryConfig = {
      maxAttempts: this.validateMaxAttempts(config.maxAttempts, defaults.maxAttempts),
      baseDelay: this.validateDelay(config.baseDelay, defaults.baseDelay, 'baseDelay'),
      maxDelay: this.validateDelay(config.maxDelay, defaults.maxDelay, 'maxDelay'),
      exponentialFactor: this.validateExponentialFactor(
        config.exponentialFactor,
        defaults.exponentialFactor
      ),
      timeout: this.validateTimeout(config.timeout, defaults.timeout),
    };

    // 确保maxDelay >= baseDelay
    if (validated.maxDelay < validated.baseDelay) {
      this.logger.warn('maxDelay小于baseDelay，自动调整', {
        originalMaxDelay: validated.maxDelay,
        baseDelay: validated.baseDelay,
      });
      validated.maxDelay = Math.max(validated.baseDelay * 10, 30000);
    }

    this.logger.debug('重试配置验证完成', validated);
    return validated;
  }

  /**
   * 验证最大重试次数
   */
  private static validateMaxAttempts(value: number | undefined, defaultValue: number): number {
    if (value === undefined || value === null) {
      return defaultValue;
    }

    if (typeof value !== 'number' || !Number.isInteger(value)) {
      this.logger.warn('maxAttempts必须是整数，使用默认值', { value, defaultValue });
      return defaultValue;
    }

    if (value < 0) {
      this.logger.warn('maxAttempts不能为负数，使用默认值', { value, defaultValue });
      return defaultValue;
    }

    if (value === Infinity || value > 100) {
      this.logger.warn('maxAttempts过大，限制为合理范围', { value, limitedValue: 10 });
      return 10;
    }

    return value;
  }

  /**
   * 验证延迟时间
   */
  private static validateDelay(
    value: number | undefined,
    defaultValue: number,
    fieldName: string
  ): number {
    if (value === undefined || value === null) {
      return defaultValue;
    }

    if (typeof value !== 'number' || value < 0) {
      this.logger.warn(`${fieldName}必须是非负数，使用默认值`, { value, defaultValue });
      return defaultValue;
    }

    if (value > 300000) {
      // 最大5分钟
      this.logger.warn(`${fieldName}过大，限制为5分钟`, { value, limitedValue: 300000 });
      return 300000;
    }

    return value;
  }

  /**
   * 验证指数因子
   */
  private static validateExponentialFactor(
    value: number | undefined,
    defaultValue: number
  ): number {
    if (value === undefined || value === null) {
      return defaultValue;
    }

    if (typeof value !== 'number' || value <= 1) {
      this.logger.warn('exponentialFactor必须大于1，使用默认值', { value, defaultValue });
      return defaultValue;
    }

    if (value > 3) {
      this.logger.warn('exponentialFactor过大，限制为3', { value, limitedValue: 3 });
      return 3;
    }

    return value;
  }

  /**
   * 验证超时时间
   */
  private static validateTimeout(value: number | undefined, defaultValue: number): number {
    if (value === undefined || value === null) {
      return defaultValue;
    }

    if (typeof value !== 'number' || value <= 0) {
      this.logger.warn('timeout必须是正数，使用默认值', { value, defaultValue });
      return defaultValue;
    }

    if (value > 600000) {
      // 最大10分钟
      this.logger.warn('timeout过大，限制为10分钟', { value, limitedValue: 600000 });
      return 600000;
    }

    return value;
  }

  /**
   * 计算下次重试延迟（指数退避）
   */
  static calculateRetryDelay(attempt: number, config: RetryConfig): number {
    if (attempt <= 0) {
      return config.baseDelay;
    }

    const exponentialDelay = config.baseDelay * Math.pow(config.exponentialFactor, attempt - 1);
    const delayWithJitter = exponentialDelay + Math.random() * 1000; // 添加随机抖动

    return Math.min(delayWithJitter, config.maxDelay);
  }

  /**
   * 验证WebSocket配置
   */
  static validateWebSocketConfig(config: any): any {
    const defaults = {
      maxReconnectAttempts: 5,
      reconnectInterval: 3000,
      connectionTimeout: 10000,
    };

    return {
      ...config,
      maxReconnectAttempts: this.validateMaxAttempts(
        config.maxReconnectAttempts,
        defaults.maxReconnectAttempts
      ),
      reconnectInterval: this.validateDelay(
        config.reconnectInterval,
        defaults.reconnectInterval,
        'reconnectInterval'
      ),
      connectionTimeout: this.validateTimeout(config.connectionTimeout, defaults.connectionTimeout),
    };
  }

  /**
   * 验证TTS配置
   */
  static validateTTSConfig(config: any): any {
    const defaults = {
      maxReconnectAttempts: 3,
      reconnectDelay: 3000,
      connectionTimeout: 10000,
      heartbeatInterval: 30000,
    };

    return {
      ...config,
      maxReconnectAttempts: this.validateMaxAttempts(
        config.maxReconnectAttempts,
        defaults.maxReconnectAttempts
      ),
      reconnectDelay: this.validateDelay(
        config.reconnectDelay,
        defaults.reconnectDelay,
        'reconnectDelay'
      ),
      connectionTimeout: this.validateTimeout(config.connectionTimeout, defaults.connectionTimeout),
      heartbeatInterval: this.validateDelay(
        config.heartbeatInterval,
        defaults.heartbeatInterval,
        'heartbeatInterval'
      ),
    };
  }

  /**
   * 检查配置是否可能导致无限重试
   */
  static checkForInfiniteRetryRisk(config: any, configType: string): boolean {
    const risks: string[] = [];

    if (config.maxReconnectAttempts === Infinity || config.maxReconnectAttempts > 50) {
      risks.push(`${configType}.maxReconnectAttempts过大或为无限: ${config.maxReconnectAttempts}`);
    }

    if (config.maxRetries === Infinity || config.maxRetries > 50) {
      risks.push(`${configType}.maxRetries过大或为无限: ${config.maxRetries}`);
    }

    if (config.reconnectInterval && config.reconnectInterval < 100) {
      risks.push(`${configType}.reconnectInterval过小: ${config.reconnectInterval}ms`);
    }

    if (risks.length > 0) {
      this.logger.error('检测到无限重试风险', { configType, risks });
      return true;
    }

    return false;
  }
}
