/**
 * 服务协调器
 * 协调HKSTT和AI服务客户端的交互
 */

import { EventBus } from '../core/EventBus';
import { Logger, LogLevel } from '../utils/Logger';

import { AIClient } from './AIClient';
import { CosyVoiceTTSService } from './CosyVoiceTTSService';
import { HKSTTClient } from './HKSTTClient';
import { HttpSSETTSService } from './HttpSSETTSService';
import type { ITTSService, TTSConfig } from './types';

/**
 * 服务协调器配置
 */
export interface ServiceCoordinatorConfig {
  /** 调试模式 */
  debug?: boolean;
  /** TTS服务配置 */
  tts?: TTSConfig;
}

/**
 * 服务协调器
 */
export class ServiceCoordinator {
  private eventBus: EventBus;
  private logger: Logger;
  private hksttClient: HKSTTClient;
  private aiClient: AIClient;
  private ttsService: ITTSService | null = null;

  // 新用户检测状态
  private lastFaceStatus: boolean | null = null;

  // 语言模式状态
  private currentLanguage: 'mandarin' | 'sichuan' = 'mandarin';

  constructor(
    config: ServiceCoordinatorConfig,
    eventBus: EventBus,
    hksttClient: HKSTTClient,
    aiClient: AIClient
  ) {
    this.eventBus = eventBus;
    this.hksttClient = hksttClient;
    this.aiClient = aiClient;

    this.logger = Logger.getInstance({
      level: config.debug ? LogLevel.DEBUG : LogLevel.INFO,
      prefix: 'ServiceCoordinator',
    });

    // 初始化TTS服务
    if (config.tts) {
      this.ttsService = this.createTTSService(config.tts, eventBus);
      this.logger.info('TTS服务已初始化', { protocol: config.tts.protocol || 'http-sse' });
    }

    this.bindEvents();
  }

  /**
   * 创建TTS服务实例
   */
  private createTTSService(config: TTSConfig, eventBus: EventBus): ITTSService {
    const protocol = config.protocol || 'http-sse'; // 默认使用HTTP+SSE

    if (protocol === 'websocket') {
      this.logger.info('创建WebSocket TTS服务');
      return new CosyVoiceTTSService(config, eventBus);
    } else if (protocol === 'http-sse') {
      this.logger.info('创建HTTP+SSE TTS服务');
      return new HttpSSETTSService(
        {
          serverUrl: config.serverUrl,
          requestTimeout: config.requestTimeout || 30000,
          maxRetryAttempts: config.maxReconnectAttempts || 3,
          retryDelay: config.reconnectDelay || 1000,
          seed: config.seed || 42,
          debug: config.debug || false,
        },
        eventBus
      );
    } else {
      throw new Error(`不支持的TTS协议: ${protocol}`);
    }
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    // 监听AI聊天请求事件（经过MessageRouter处理的内部事件）
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- 事件数据结构复杂
    this.eventBus.on('ai:send-chat-request-internal', (data: any) => {
      this.handleChatRequest(data);
    });

    // 监听HKSTT ASR结果事件
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- ASR 结果格式多样
    this.eventBus.on('hkstt:asr-offline-result', (data: any) => {
      this.handleASRResult(data);
    });

    // 监听HKSTT人脸状态事件 - 直接处理（ASRHandler已移除）
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- 人脸状态数据格式多样
    this.eventBus.on('hkstt:face-status', (data: any) => {
      this.handleFaceStatusFromHKSTT(data);
    });

    // 监听AI响应事件
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- AI 响应格式多样
    this.eventBus.on('ai:chat-response', (data: any) => {
      this.handleAIResponse(data);
    });

    // 监听AI流式响应事件
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- AI 流式数据格式多样
    this.eventBus.on('ai:chat-stream-chunk', (data: any) => {
      this.handleAIStreamChunk(data);
    });

    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- AI 流式完成数据格式多样
    this.eventBus.on('ai:chat-stream-complete', (data: any) => {
      this.handleAIStreamComplete(data);
    });

    // 监听AI错误事件
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- AI 错误数据格式多样
    this.eventBus.on('ai:chat-error', (data: any) => {
      this.handleAIError(data);
    });

    // 监听AI流式响应完成事件，触发TTS播放
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- AI 流式完成数据格式多样
    this.eventBus.on('ai:chat-final-response', (data: any) => {
      this.handleTTSRequest(data);
    });

    // 监听语言模式变化事件
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- 语言变化数据格式简单
    this.eventBus.on('tts:language-change', (data: any) => {
      this.handleLanguageChange(data);
    });
  }

  /**
   * 处理聊天请求
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- 聊天请求数据结构复杂
  private async handleChatRequest(data: any): Promise<void> {
    try {
      const { userInput, sessionId, requestId } = data;

      this.logger.info('处理聊天请求', {
        userInput: userInput.substring(0, 50) + '...',
        sessionId,
        requestId,
      });

      // 调用AI客户端发送聊天请求
      await this.aiClient.sendChatRequest({
        userInput,
        sessionId,
        requestId,
      });
    } catch (error) {
      this.logger.error('处理聊天请求失败', { error, data });

      // 发送错误事件
      this.eventBus.emit('service:chat-request-error', {
        error,
        requestId: data.requestId,
      });
    }
  }

  /**
   * 处理ASR识别结果
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- ASR 结果数据格式多样
  private handleASRResult(data: any): void {
    const { sid, text } = data;

    this.logger.info('处理ASR识别结果', {
      sid,
      text: text.substring(0, 50) + '...',
    });

    // 注意：不再在这里处理ASR结果，已由MessageRouter统一处理
    // 这里只做日志记录，避免重复处理
    this.logger.debug('ASR结果已由MessageRouter处理，跳过重复处理');
  }

  /**
   * 处理来自HKSTT的人脸状态事件（包含新用户检测逻辑）
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- 人脸状态数据格式多样
  private handleFaceStatusFromHKSTT(data: any): void {
    const { hasFace, timestamp } = data;
    const currentTime = Date.now();

    this.logger.info('收到HKSTT人脸状态事件', {
      hasFace,
      timestamp,
      lastFaceStatus: this.lastFaceStatus,
    });

    // 检测是否为新用户（从无人到有人的状态变化）
    const isNewUser = this.lastFaceStatus === false && hasFace === true;

    // 更新状态
    this.lastFaceStatus = hasFace;

    // 发送人脸状态变化事件给其他组件
    this.eventBus.emit('face:status-updated', {
      hasFace,
      timestamp: timestamp || currentTime,
      isNewUser,
    });

    // 如果检测到新用户，发送新用户通知
    if (isNewUser) {
      this.logger.info('🎉 检测到新用户进入', {
        hasFace,
        timestamp: timestamp || currentTime,
      });

      // 通过NotificationService发送新用户通知
      this.eventBus.emit('notification:send-to-client', {
        method: 'notifications/newUser',
        params: {
          timestamp: timestamp || currentTime,
          hasFace,
          message: '检测到新用户进入',
        },
      });
    }
  }

  /**
   * 处理AI响应
   */
  private handleAIResponse(data: any): void {
    const { message, sessionId, requestId } = data;

    this.logger.info('处理AI响应', {
      requestId,
      sessionId,
      messageLength: message.length,
    });

    // 转发给路由器处理
    this.eventBus.emit('ai:chat-response-processed', {
      message,
      sessionId,
      requestId,
    });
  }

  /**
   * 处理AI流式响应片段
   */
  private handleAIStreamChunk(data: any): void {
    const { message, requestId, sessionId } = data;

    this.logger.debug('📈 处理AI流式响应片段', {
      requestId,
      sessionId,
      message: message || '(空消息)',
      messageLength: message?.length || 0,
    });

    // 发送流式进度通知
    this.eventBus.emit('notifications/progress', {
      message,
      requestId,
      sessionId,
    });

    // 如果消息不为空，也发送给UI显示（实时更新）
    if (message && message.trim()) {
      this.logger.debug('📤 发送流式消息片段到UI', {
        requestId,
        sessionId,
        messageFragment: message,
      });

      // 发送流式消息片段事件，让UI可以实时显示
      this.eventBus.emit('ai:stream-message-chunk', {
        message,
        requestId,
        sessionId,
        isPartial: true,
      });
    }
  }

  /**
   * 处理AI流式响应完成
   */
  private handleAIStreamComplete(data: any): void {
    const { message, requestId, sessionId } = data;

    this.logger.info('处理AI流式响应完成', {
      requestId,
      sessionId,
      messageLength: message.length,
    });

    // 发送最终响应
    this.eventBus.emit('ai:chat-final-response', {
      message,
      requestId,
      sessionId,
    });
  }

  /**
   * 处理AI错误
   */
  private handleAIError(data: any): void {
    const { error, requestId } = data;

    this.logger.error('处理AI错误', { error: error.message, requestId });

    // 发送错误通知
    this.eventBus.emit('notifications/error', {
      error: error.message,
      requestId,
    });
  }

  /**
   * 处理语言模式变化
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- 语言变化数据格式简单
  private handleLanguageChange(data: any): void {
    const { language } = data;
    if (language && (language === 'mandarin' || language === 'sichuan')) {
      this.currentLanguage = language;
      this.logger.info('语言模式已切换', { language });
    }
  }

  /**
   * 获取当前语言对应的TTS指令文本
   * 根据新API文档更新指令格式
   */
  private getTTSInstructText(): string {
    switch (this.currentLanguage) {
      case 'sichuan':
        return '用四川话说这句话';
      case 'mandarin':
      default:
        return '用自然清晰的普通话说话';
    }
  }

  /**
   * 处理TTS请求
   */
  private async handleTTSRequest(data: any): Promise<void> {
    if (!this.ttsService) {
      this.logger.warn('TTS服务未初始化，跳过语音合成');
      return;
    }

    try {
      const { message, requestId, sessionId } = data;

      this.logger.info('开始TTS语音合成', {
        requestId,
        sessionId,
        messageLength: message.length,
        language: this.currentLanguage,
      });

      // 根据当前语言模式获取指令文本
      const instructText = this.getTTSInstructText();

      // 调用TTS服务进行语音合成
      await this.ttsService.speak(message, instructText);

      this.logger.info('TTS语音合成请求已发送', { requestId, instructText });
    } catch (error) {
      this.logger.error('TTS语音合成失败', { error, data });

      // 发送TTS错误事件
      this.eventBus.emit('tts:synthesis-error', {
        error,
        requestId: data.requestId,
      });
    }
  }

  /**
   * 发送ping到HKSTT服务器
   */
  public async pingHKSTT(): Promise<void> {
    try {
      await this.hksttClient.ping();
      this.logger.info('HKSTT ping成功');
    } catch (error) {
      this.logger.error('HKSTT ping失败', error);
      throw error;
    }
  }

  /**
   * 获取HKSTT服务状态
   */
  public async getHKSTTStatus(): Promise<void> {
    try {
      await this.hksttClient.getStatus();
      this.logger.info('获取HKSTT状态成功');
    } catch (error) {
      this.logger.error('获取HKSTT状态失败', error);
      throw error;
    }
  }

  /**
   * 停止所有AI流式响应
   */
  public stopAllAIStreams(): void {
    this.aiClient.stopAllStreams();
    this.logger.info('已停止所有AI流式响应');
  }

  /**
   * 停止TTS播放
   */
  public stopTTS(): void {
    if (this.ttsService) {
      this.ttsService.stop();
      this.logger.info('TTS播放已停止');
    }
  }

  /**
   * 停止HKSTT收音会话
   */
  public async stopHKSTTSession(): Promise<void> {
    try {
      this.logger.info('🛑 开始停止HKSTT收音会话');
      await this.hksttClient.stopSession();
      this.logger.info('✅ HKSTT收音会话已停止');
    } catch (error) {
      this.logger.error('❌ 停止HKSTT收音会话失败', error);
      throw error;
    }
  }

  /**
   * 暂停TTS播放
   */
  public pauseTTS(): void {
    if (this.ttsService) {
      this.ttsService.pause();
      this.logger.info('TTS播放已暂停');
    }
  }

  /**
   * 继续TTS播放
   */
  public resumeTTS(): void {
    if (this.ttsService) {
      this.ttsService.resume();
      this.logger.info('TTS播放已继续');
    }
  }

  /**
   * 获取服务状态
   */
  public getServiceStatus(): {
    hkstt: { connected: boolean; state: string };
    ai: { activeStreams: number };
    tts: { connected: boolean; status: string } | null;
  } {
    return {
      hkstt: {
        connected: this.hksttClient.isConnected(),
        state: this.hksttClient.getState(),
      },
      ai: {
        activeStreams: this.aiClient.getActiveStreamCount(),
      },
      tts: this.ttsService
        ? {
            connected: this.ttsService.isConnected(),
            status: this.ttsService.getStatus(),
          }
        : null,
    };
  }

  /**
   * 销毁协调器
   */
  public destroy(): void {
    if (this.ttsService) {
      this.ttsService.destroy();
      this.ttsService = null;
    }
    this.logger.info('服务协调器已销毁');
  }
}
