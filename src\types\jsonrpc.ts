/**
 * JSON-RPC 2.0 相关类型定义
 * 严格遵循JSON-RPC 2.0规范和json-rpc.md文档
 */

// ============================================================================
// 基础JSON-RPC 2.0类型
// ============================================================================

export interface JsonRpcRequest {
  jsonrpc: '2.0';
  method: string;
  params?: unknown;
  id: string | number;
}

export interface JsonRpcResponse {
  jsonrpc: '2.0';
  result?: unknown;
  error?: JsonRpcError;
  id: string | number;
}

export interface JsonRpcNotification {
  jsonrpc: '2.0';
  method: string;
  params?: unknown;
  // 注意：通知没有id字段
}

export interface JsonRpcError {
  code: number;
  message: string;
  data?: unknown;
}

// ============================================================================
// 新增：原生JSON-RPC消息接口
// ============================================================================

/** 完整的JSON-RPC 2.0请求消息 */
export interface JsonRpcMessage {
  /** JSON-RPC版本，必须为"2.0" */
  jsonrpc: '2.0';
  /** 方法名 */
  method: string;
  /** 参数（可选） */
  params?: any;
  /** 请求ID，用户完全控制 */
  id: string | number;
}

/** JSON-RPC消息发送选项 */
export interface JsonRpcMessageOptions {
  /** 超时时间（毫秒），默认30秒 */
  timeout?: number;
}

// ============================================================================
// 文档中定义的具体方法参数类型
// ============================================================================

/** speak方法参数 */
export interface SpeakParams {
  /** 说话内容，编码UTF-8，必填 */
  text: string;
  /** 非负整数，延时delay毫秒数后说话，可选，缺省值为0 */
  delay?: number;
  /** 是否显示到气泡上，可选，缺省值为true */
  display?: boolean;
}

/** updateBackgroundInfo方法参数 */
export interface UpdateBackgroundInfoParams {
  /** 会话id，如果传的有值，则找到对应会话更新数据，如果没传值，会在响应消息里带上sessionId */
  sessionId?: string;
  /** 当前的页面Id */
  page?: string;
  /** 当前状态id */
  status?: string;
}

/** addMessages方法参数 */
export interface AddMessagesParams {
  /** 会话id，如果传的有值，则找到对应会话更新数据，如果没传值，会在响应消息里带上sessionId */
  sessionId?: string;
  /** 消息列表，必须符合OpenAI的消息格式 */
  messages: Array<{
    role: 'user' | 'assistant';
    content: string;
  }>;
}

/** pushBizData方法参数 */
export interface PushBizDataParams {
  /** 业务数据的key值 */
  key: string;
  /** 业务数据 */
  data: unknown;
}

/** chat方法参数（AI服务器） */
export interface ChatParams {
  /** 用户语音输入的内容 */
  userInput: string;
  /** 会话Id */
  sessionId?: string;
}

// ============================================================================
// AI响应Action相关类型
// ============================================================================

/** AI响应结果基础类型 */
export interface AIResponseResult {
  /** 响应消息 */
  message: string;
  /** 操作类型 */
  action?: string;
  /** 操作数据 */
  data?: unknown;
  /** 更新类型（用于update action） */
  type?: string;
  /** 链式响应的下一个请求ID */
  nextRequestId?: string;
}

/** 业务操作Action类型 */
export type BusinessAction = 'login' | 'register' | 'submitOrder';

/** 数据更新Action类型 */
export type UpdateAction = 'update';

/** 数据更新类型 */
export type UpdateType = 'form' | 'filters' | 'settings';

/** 登录Action数据 */
export interface LoginActionData {
  /** 登录类型 */
  type: 'idCard' | 'password' | 'biometric';
}

/** 注册Action数据 */
export interface RegisterActionData {
  /** 用户类型 */
  userType: 'individual' | 'enterprise';
}

/** 提交订单Action数据 */
export interface SubmitOrderActionData {
  /** 订单类型 */
  orderType?: string;
  /** 是否需要确认 */
  confirmRequired?: boolean;
}

/** 表单更新数据 */
export interface FormUpdateData {
  [key: string]: unknown;
}

/** 筛选器更新数据 */
export interface FiltersUpdateData {
  [key: string]: unknown;
}

/** 设置更新数据 */
export interface SettingsUpdateData {
  [key: string]: unknown;
}

// ============================================================================
// 通知相关类型
// ============================================================================

/** 流式响应进度通知参数 */
export interface ProgressNotificationParams {
  /** 流式消息内容 */
  message: string;
  /** 原始请求ID */
  requestId: string;
}

/** 状态通知参数 */
export interface StatusNotificationParams {
  /** 原始请求ID */
  requestId: string;
  /** 状态消息 */
  message: string;
}

/** 聊天流式响应通知参数 */
export interface ChatStreamResponseParams {
  /** 流式返回文本 */
  message: string;
  /** 请求id */
  requestId: string;
  /** 会话Id */
  sessionId: string;
}

/** 用户输入通知参数 */
export interface UserInputNotificationParams {
  /** 用户语音输入的内容 */
  userInput: string;
  /** 请求id */
  requestId: string;
  /** 会话Id */
  sessionId: string;
}

/** 新用户通知参数 */
export interface NewUserNotificationParams {
  // 目前文档中没有具体参数定义，使用Record类型
  [key: string]: unknown;
}

/** 人脸状态通知参数 */
export interface FaceStatusNotificationParams {
  /** 是否有人脸 */
  hasFace: boolean;
}

/** 模型状态通知参数 */
export interface ModelStatusNotificationParams {
  /** 模型是否已加载 */
  loaded: boolean;
}

/** ASR离线识别结果通知参数 */
export interface ASROfflineResultParams {
  /** 会话ID */
  sid: string;
  /** 识别文本 */
  text: string;
}

/** ASR会话完成通知参数 */
export interface ASRSessionCompleteParams {
  // 目前文档中没有具体参数定义，使用Record类型
  [key: string]: unknown;
}

// ============================================================================
// 请求选项和回调函数类型
// ============================================================================

/** JSON-RPC请求选项 */
export interface JsonRpcRequestOptions {
  /** 超时时间（毫秒），默认30秒 */
  timeout?: number;
}

/** 通知回调函数类型 */
export type NotificationCallback = (params: unknown) => void;

/** Action回调函数类型 */
export type ActionCallback = (result: AIResponseResult) => void;

// ============================================================================
// 错误码常量
// ============================================================================

/** JSON-RPC 2.0 标准错误码 */
export const JsonRpcErrorCodes = {
  /** 解析错误 - JSON格式错误、语法错误 */
  PARSE_ERROR: -32700,
  /** 无效请求 - 缺少必需字段、字段类型错误 */
  INVALID_REQUEST: -32600,
  /** 方法未找到 - 调用不存在的方法 */
  METHOD_NOT_FOUND: -32601,
  /** 无效参数 - 参数类型错误、缺少必需参数 */
  INVALID_PARAMS: -32602,
  /** 内部错误 - 服务器内部处理错误 */
  INTERNAL_ERROR: -32603,
} as const;
