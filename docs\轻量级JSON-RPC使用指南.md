# 增强的JSON-RPC使用指南

## 概述

WebSDK提供了一个增强的JSON-RPC 2.0实现，完全兼容现有API，同时增加了强大的新功能。该实现严格遵循JSON-RPC 2.0规范，支持AI响应action处理、链式响应和完整的通知系统。

## 设计原则

1. **完全向后兼容** - 现有使用`sdk.sendRequest()`和`sdk.onNotification()`的代码无需修改
2. **严格遵循JSON-RPC 2.0规范** - 确保请求和响应格式完全符合标准
3. **增强而非替换** - 在现有功能基础上增加新特性，不破坏现有API
4. **智能action处理** - 自动处理AI服务器返回的各种action类型
5. **复用现有基础设施** - 基于EventBus实现，与现有系统完美兼容

## 基本使用

### 1. 发送JSON-RPC请求（增强版sendRequest）

```typescript
import { init } from 'web-service-api-sdk';

// 初始化SDK
const sdk = await init({
  hksttUrl: 'ws://localhost:8000/ws',
  aiServerUrl: 'http://localhost:3000',
  debug: true
});

// 发送JSON-RPC请求 - 现在支持Promise返回
try {
  const result = await sdk.sendRequest('ai/chat', {
    message: '我想查询余额'
  });
  console.log('AI响应:', result);
} catch (error) {
  console.error('请求失败:', error.message);
}

// 支持自定义超时
const result = await sdk.sendRequest('speak', {
  text: '您好，欢迎使用我们的服务'
}, { timeout: 10000 });

// 支持自定义请求ID（高级用法）
const result2 = await sdk.sendRequest('updateBackgroundInfo', {
  userInfo: { name: '张三' }
}, {
  id: 'user-update-001', // 自定义ID，用于请求去重或调试
  timeout: 15000
});

// 生成唯一请求ID（防止冲突）
const uniqueId = sdk.generateRequestId('order'); // 生成如 "order_abc123def"
const result3 = await sdk.sendRequest('submitOrder', {
  orderData: { amount: 100 }
}, { id: uniqueId });

// 支持文档中定义的所有方法
await sdk.sendRequest('updateBackgroundInfo', {
  userInfo: { name: '张三', age: 30 },
  context: '用户咨询业务办理'
});

await sdk.sendRequest('addMessages', {
  messages: [
    { role: 'user', content: '我想查询余额' },
    { role: 'assistant', content: '好的，我来帮您查询' }
  ]
});

await sdk.sendRequest('pushBizData', {
  type: 'userProfile',
  data: { userId: '12345', accountType: 'premium' }
});
```

### 2. 监听JSON-RPC通知

```typescript
// 监听AI服务器的各种通知
const unsubscribe = sdk.onNotification('notifications/progress', (params) => {
  console.log('流式响应进度:', params.message);
});

// 监听用户输入事件
sdk.onNotification('notifications/userInput', (params) => {
  console.log('用户语音输入:', params.userInput);
  console.log('会话ID:', params.sessionId);
});

// 监听ASR识别结果
sdk.onNotification('notifications/asrOfflineResult', (params) => {
  console.log('ASR识别结果:', params.text);
});

// 监听人脸状态变化
sdk.onNotification('notifications/faceStatus', (params) => {
  console.log('人脸检测状态:', params.hasFace ? '有人脸' : '无人脸');
});

// 监听新用户事件
sdk.onNotification('notifications/newUser', (params) => {
  console.log('检测到新用户:', params);
});

// 取消监听
unsubscribe();
```

## AI Action处理

WebSDK会自动处理AI服务器返回的各种action，并发送相应的事件：

### 1. 业务操作Action

```typescript
// 监听登录action
sdk.getEventBus().on('ai:action:login', (data) => {
  console.log('AI要求用户登录:', data.data);
  // 执行登录逻辑
  showLoginDialog(data.data.type); // 如: 'idCard', 'password'
});

// 监听注册action
sdk.getEventBus().on('ai:action:register', (data) => {
  console.log('AI要求用户注册:', data.data);
  // 执行注册逻辑
  showRegisterDialog(data.data);
});

// 监听提交订单action
sdk.getEventBus().on('ai:action:submitOrder', (data) => {
  console.log('AI要求提交订单:', data.data);
  // 执行订单提交逻辑
  submitOrder(data.data);
});
```

### 2. 数据更新Action

```typescript
// 监听表单更新（增量更新）
sdk.getEventBus().on('ai:action:update:form', (data) => {
  console.log('AI更新表单数据:', data.data);
  // 只更新提到的字段
  updateFormFields(data.data, 'incremental');
});

// 监听筛选条件更新（全量更新）
sdk.getEventBus().on('ai:action:update:filters', (data) => {
  console.log('AI更新筛选条件:', data.data);
  // 更新全部筛选条件
  updateFilters(data.data, 'full');
});

// 监听配置更新（增量更新）
sdk.getEventBus().on('ai:action:update:settings', (data) => {
  console.log('AI更新配置:', data.data);
  // 只更新提到的配置项
  updateSettings(data.data, 'incremental');
});
```

### 3. 链式响应处理（自动管理）

```typescript
// 链式响应现在由SDK自动管理，无需手动处理ID
// 当AI返回包含nextRequestId的响应时，SDK会自动注册后续响应ID

// 发送可能产生链式响应的请求
const result = await sdk.sendRequest('ai/chat', {
  message: '我要注册新账户'
});

if (result.nextRequestId) {
  console.log('这个请求会产生后续响应:', result.nextRequestId);
  // SDK已自动将nextRequestId加入pending列表，无需手动处理
}

// 监听链式响应事件（可选）
sdk.getEventBus().on('ai:chained-request', (data) => {
  console.log('检测到链式响应:', {
    nextId: data.requestId,
    parentId: data.parentId,
    timestamp: data.timestamp
  });
});

// 所有action处理保持不变
sdk.getEventBus().on('ai:action:register', (data) => {
  console.log('AI要求注册:', data.data);
  // 执行注册逻辑...
});

sdk.getEventBus().on('ai:action:update:form', (data) => {
  console.log('AI更新表单:', data.data);
  // 更新表单逻辑...
});

// 重要：页面跳转时清空所有pending请求，包括链式响应
function onNavigate() {
  sdk.clearPendingRequests(); // 这会取消所有pending请求，包括链式响应
}
```

## 高级功能

### 1. 错误处理

```typescript
try {
  const result = await sdk.sendRequest('invalid.method');
} catch (error) {
  if (error.code === -32601) {
    console.log('方法不存在');
  } else if (error.code === -32602) {
    console.log('参数无效');
  } else {
    console.log('其他错误:', error.message);
  }
}
```

### 2. 并发请求

```typescript
// 并发发送多个请求
const promises = [
  sdk.sendRequest('speak', { text: '欢迎使用' }),
  sdk.sendRequest('updateBackgroundInfo', { userInfo: { name: '用户' } }),
  sdk.sendRequest('pushBizData', { type: 'status', data: { online: true } })
];

const results = await Promise.all(promises);
console.log('所有结果:', results);
```

### 3. 特殊通知处理

```typescript
// 监听流式响应进度
sdk.getEventBus().on('ai:stream:progress', (data) => {
  console.log('流式响应:', data.message);
  // 实时显示AI回复内容
  appendToChat(data.message);
});

// 监听状态更新
sdk.getEventBus().on('ai:status', (data) => {
  console.log('AI状态:', data.message);
  // 显示处理状态
  showStatus(data.message);
});

// 监听TTS流式文本
sdk.getEventBus().on('tts:stream-text', (data) => {
  console.log('TTS文本:', data.text);
  // 发送给TTS引擎
  speakText(data.text);
});
```

## JSON-RPC 2.0规范遵循

### 请求格式

```json
{
  "jsonrpc": "2.0",
  "method": "user.getInfo",
  "params": { "userId": 123 },
  "id": "abc123"
}
```

### 成功响应格式

```json
{
  "jsonrpc": "2.0",
  "result": {
    "id": 123,
    "name": "张三",
    "email": "<EMAIL>"
  },
  "id": "abc123"
}
```

### 错误响应格式

```json
{
  "jsonrpc": "2.0",
  "error": {
    "code": -32601,
    "message": "Method not found",
    "data": { "method": "invalid.method" }
  },
  "id": "abc123"
}
```

### 标准错误码

| 错误码 | 错误名称 | 描述 |
|--------|----------|------|
| -32700 | Parse error | JSON解析错误 |
| -32600 | Invalid Request | 无效请求 |
| -32601 | Method not found | 方法未找到 |
| -32602 | Invalid params | 无效参数 |
| -32603 | Internal error | 内部错误 |

## 最佳实践

### 1. 统一错误处理

```typescript
async function safeRequest(method: string, params?: any) {
  try {
    return await sdk.sendRequest(method, params);
  } catch (error) {
    // 记录错误
    console.error(`JSON-RPC请求失败: ${method}`, error);

    // 根据错误类型处理
    if (error.code === -32601) {
      throw new Error('请求的功能不存在');
    } else if (error.code === -32602) {
      throw new Error('请求参数有误');
    } else {
      throw new Error('服务暂时不可用，请稍后重试');
    }
  }
}
```

### 2. AI Action统一处理

```typescript
// 创建统一的action处理器
class AIActionHandler {
  constructor(private sdk: WebSDK) {
    this.setupActionListeners();
  }

  private setupActionListeners() {
    const eventBus = this.sdk.getEventBus();

    // 统一处理所有登录相关action
    eventBus.on('ai:action:login', (data) => {
      this.handleLogin(data.data, data.message);
    });

    // 统一处理所有更新action
    eventBus.on('ai:action:update:form', (data) => {
      this.handleFormUpdate(data.data, data.updateType);
    });

    eventBus.on('ai:action:update:filters', (data) => {
      this.handleFiltersUpdate(data.data, data.updateType);
    });

    // 处理未知action
    eventBus.on('ai:action', (data) => {
      console.warn('未处理的AI action:', data.action);
      this.handleUnknownAction(data);
    });
  }

  private handleLogin(data: any, message: string) {
    // 统一的登录处理逻辑
    console.log('处理登录:', message, data);
  }

  private handleFormUpdate(data: any, updateType: string) {
    // 统一的表单更新逻辑
    console.log('更新表单:', updateType, data);
  }

  private handleFiltersUpdate(data: any, updateType: string) {
    // 统一的筛选器更新逻辑
    console.log('更新筛选器:', updateType, data);
  }

  private handleUnknownAction(data: any) {
    // 处理未知action的默认逻辑
    console.log('未知action:', data);
  }
}

// 使用
const actionHandler = new AIActionHandler(sdk);
```

### 3. 超时配置

```typescript
// 根据操作类型设置不同超时
const TIMEOUTS = {
  quick: 5000,    // 快速操作：5秒
  normal: 15000,  // 普通操作：15秒
  slow: 60000     // 慢操作：60秒
};

const userInfo = await sdk.sendRequest('user.getInfo', { userId }, { timeout: TIMEOUTS.quick });
const report = await sdk.sendRequest('report.generate', { type: 'monthly' }, { timeout: TIMEOUTS.slow });
```

### 4. 请求生命周期管理

```typescript
// 页面跳转或用户取消操作时，清空所有待处理请求
// 这是JSON-RPC文档中要求的核心功能，防止收到过期响应
function onPageChange() {
  sdk.clearPendingRequests();
  console.log('已清空所有待处理请求');
}

// 查看当前待处理请求状态（调试用）
function debugPendingRequests() {
  const pending = sdk.getPendingRequestsInfo();
  console.log('当前待处理请求:', pending);

  pending.forEach(req => {
    console.log(`- ${req.id}: ${req.method} (${req.type})`);
    if (req.parentId) {
      console.log(`  └─ 链式响应，父请求: ${req.parentId}`);
    }
  });
}

// 在单页应用中的使用示例
window.addEventListener('beforeunload', () => {
  sdk.clearPendingRequests();
});

// 在React/Vue等框架中的使用示例
useEffect(() => {
  return () => {
    // 组件卸载时清空请求
    sdk.clearPendingRequests();
  };
}, []);
```

### 5. 请求ID管理（高级用法）

```typescript
// 场景1：防止重复提交
async function submitFormWithDeduplication(formData: any) {
  const formId = `form-${formData.id}-${Date.now()}`;

  // 检查是否已有相同请求在处理
  if (sdk.hasRequestId(formId)) {
    console.log('请求正在处理中，请勿重复提交');
    return;
  }

  try {
    const result = await sdk.sendRequest('submitForm', formData, {
      id: formId,
      timeout: 30000
    });
    console.log('表单提交成功:', result);
  } catch (error) {
    console.error('表单提交失败:', error);
  }
}

// 场景2：批量请求管理
async function batchProcessWithCustomIds() {
  const tasks = [
    { id: 'task-001', data: { type: 'A' } },
    { id: 'task-002', data: { type: 'B' } },
    { id: 'task-003', data: { type: 'C' } }
  ];

  const promises = tasks.map(task =>
    sdk.sendRequest('processTask', task.data, {
      id: task.id,
      timeout: 60000
    })
  );

  try {
    const results = await Promise.all(promises);
    console.log('批量处理完成:', results);
  } catch (error) {
    console.error('批量处理失败:', error);
  }
}

// 场景3：调试和监控
function monitorRequests() {
  const pending = sdk.getPendingRequestsInfo();

  console.log('当前待处理请求:');
  pending.forEach(req => {
    const elapsed = Date.now() - req.startTime;
    console.log(`- ${req.id}: ${req.method} (${req.type}, ${elapsed}ms)`);

    // 检查长时间运行的请求
    if (elapsed > 30000) {
      console.warn(`⚠️ 请求 ${req.id} 运行时间过长: ${elapsed}ms`);
    }
  });
}

// 场景4：生成有意义的调试ID
async function debugRequest(operation: string, data: any) {
  const debugId = sdk.generateRequestId(`debug-${operation}`);

  console.log(`🐛 开始调试请求: ${debugId}`);

  try {
    const result = await sdk.sendRequest('debug/operation', {
      operation,
      data,
      debugInfo: { requestId: debugId, timestamp: Date.now() }
    }, { id: debugId });

    console.log(`✅ 调试请求完成: ${debugId}`, result);
    return result;
  } catch (error) {
    console.error(`❌ 调试请求失败: ${debugId}`, error);
    throw error;
  }
}
```

## 故障排除

### 1. 请求超时

- 检查网络连接
- 增加超时时间
- 确认服务端处理时间

### 2. 收不到响应

- 确认EventBus事件监听正确设置
- 检查响应事件名称是否为`jsonrpc:simple-response`
- 验证响应格式是否符合JSON-RPC 2.0规范

### 3. ID匹配失败

- 确认响应中的ID与请求ID完全一致
- 检查ID类型（应为字符串）

## 新功能特性

增强的JSON-RPC实现提供以下新特性：

1. **完全向后兼容** - 现有代码无需修改，直接享受新功能
2. **智能Action处理** - 自动识别和处理AI服务器返回的各种action
3. **链式响应支持** - 支持多步骤交互的复杂业务流程
4. **丰富的通知系统** - 支持流式响应、状态更新等实时通知
5. **增强的错误处理** - 提供详细的错误信息和错误码
6. **事件驱动架构** - 基于EventBus的松耦合设计

## 迁移指南

如果您之前使用的是复杂的JsonRpcClient，迁移到新版本非常简单：

```typescript
// 旧版本（仍然支持）
const result = await sdk.sendRequest('method', params);

// 新版本增强功能（自动启用）
// - 自动处理AI action
// - 自动处理链式响应
// - 自动发送相关事件

// 监听新的事件（可选）
sdk.getEventBus().on('ai:action:login', handleLogin);
sdk.getEventBus().on('ai:stream:progress', handleProgress);
```

推荐在所有项目中使用增强版本，享受更强大的功能和更好的开发体验。
