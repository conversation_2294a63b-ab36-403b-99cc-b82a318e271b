/**
 * JSON-RPC Action处理器
 * 负责处理AI响应中的action字段
 */

import { EventBus } from '../core/EventBus';
import { Logger } from '../utils/Logger';

/**
 * Action回调函数类型
 */
export type ActionCallback = (result: any) => void;

/**
 * JSON-RPC Action处理器
 */
export class JsonRpcActionHandler {
  private eventBus: EventBus;
  private logger: Logger;
  private actionCallbacks: ActionCallback[] = [];

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
    this.logger = Logger.getInstance({ prefix: 'JsonRpcActionHandler' });

    this.setupEventListeners();
  }

  /**
   * 注册Action监听器
   * @param callback 当收到包含action的响应时调用的回调函数
   * @returns 取消监听的函数
   */
  public onAction(callback: ActionCallback): () => void {
    this.actionCallbacks.push(callback);

    this.logger.info('📝 注册Action监听器', {
      callbackCount: this.actionCallbacks.length,
    });

    // 返回取消监听的函数
    return () => {
      const index = this.actionCallbacks.indexOf(callback);
      if (index > -1) {
        this.actionCallbacks.splice(index, 1);
        this.logger.info('🗑️ 移除Action监听器', {
          remainingCount: this.actionCallbacks.length,
        });
      }
    };
  }

  /**
   * 处理包含action的响应结果
   */
  public handleActionResponse(result: any, requestId?: string): void {
    if (!result || typeof result !== 'object') {
      return;
    }

    // 检查是否包含action字段
    if (!result.action || typeof result.action !== 'string') {
      return;
    }

    this.logger.info('🎯 处理Action响应', {
      action: result.action,
      requestId,
      hasData: result.data !== undefined,
      hasMessage: result.message !== undefined,
    });

    // 调用所有注册的Action监听器
    this.actionCallbacks.forEach(callback => {
      try {
        callback(result);
      } catch (error) {
        this.logger.error('Action监听器执行失败', { error });
      }
    });

    // 发送通用的action事件（保持向后兼容）
    this.eventBus.emit('ai:action', {
      action: result.action,
      data: result.data,
      message: result.message,
      requestId,
      timestamp: Date.now(),
    });

    // 处理特定的action类型
    this.handleSpecificAction(result, requestId);
  }

  /**
   * 处理链式响应
   */
  public handleChainedResponse(nextRequestId: string): void {
    this.logger.info('🔗 处理链式响应', { nextRequestId });

    // 发送链式响应事件
    this.eventBus.emit('ai:chained-request', {
      requestId: nextRequestId,
      timestamp: Date.now(),
    });
  }

  /**
   * 销毁处理器
   */
  public destroy(): void {
    this.logger.info('🗑️ 销毁Action处理器');

    // 清空所有回调
    this.actionCallbacks = [];

    this.logger.info('✅ Action处理器已销毁');
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听JSON-RPC响应，检查是否包含action
    this.eventBus.on('jsonrpc:response', (response: any) => {
      if (response && response.result) {
        this.handleActionResponse(response.result, response.id);

        // 检查是否有nextRequestId
        if (response.result.nextRequestId) {
          this.handleChainedResponse(response.result.nextRequestId);
        }
      }
    });
  }

  /**
   * 处理特定的action类型（保持向后兼容）
   */
  private handleSpecificAction(result: any, requestId?: string): void {
    const { action, data, message, type } = result;

    switch (action) {
      case 'login':
        this.eventBus.emit('ai:action:login', {
          data,
          message,
          requestId,
          timestamp: Date.now(),
        });
        break;

      case 'register':
        this.eventBus.emit('ai:action:register', {
          data,
          message,
          requestId,
          timestamp: Date.now(),
        });
        break;

      case 'submitOrder':
        this.eventBus.emit('ai:action:submitOrder', {
          data,
          message,
          requestId,
          timestamp: Date.now(),
        });
        break;

      case 'update':
        this.handleUpdateAction(type, data, message, requestId);
        break;

      default:
        // 未知action类型，只发送通用事件
        this.logger.debug('未知的action类型', { action });
    }
  }

  /**
   * 处理更新action（支持form、filters、settings类型）
   */
  private handleUpdateAction(type: string, data: any, message: string, requestId?: string): void {
    const baseEventData = {
      data,
      message,
      requestId,
      timestamp: Date.now(),
    };

    switch (type) {
      case 'form':
        this.eventBus.emit('ai:action:update:form', {
          ...baseEventData,
          updateType: 'incremental', // 只替换有提到的值
        });
        break;

      case 'filters':
        this.eventBus.emit('ai:action:update:filters', {
          ...baseEventData,
          updateType: 'full', // 更新全部筛选条件
        });
        break;

      case 'settings':
        this.eventBus.emit('ai:action:update:settings', {
          ...baseEventData,
          updateType: 'incremental', // 只替换有提到的值
        });
        break;

      default:
        // 未知的更新类型
        this.eventBus.emit('ai:action:update', {
          ...baseEventData,
          type,
        });
        this.logger.warn('未知的更新类型', { type });
    }
  }
}
