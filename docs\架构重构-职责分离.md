# WebSDK架构重构：职责分离

## 🎯 问题分析

当前WebSDK存在严重的职责混乱问题，主要体现在：

### 1. 违反JSON-RPC规范
- **问题**：WebSDK直接处理 `handleFaceStatusChange`、`handleNewUserDetected` 等业务逻辑
- **应该**：这些应该作为JSON-RPC通知 (`notifications/faceStatus`, `notifications/newUser`) 处理

### 2. 跨层调用
- **问题**：跳过JSON-RPC层，直接在SDK层处理特定业务
- **应该**：遵循分层架构，通过标准的JSON-RPC通知机制

### 3. 单一职责原则违反
- **问题**：WebSDK既管理SDK生命周期，又处理具体业务逻辑
- **应该**：WebSDK只负责组件协调和生命周期管理

## 🏗️ 正确的架构设计

### 当前错误的流程
```
HKSTT服务器 → EventBus → WebSDK.handleFaceStatusChange() → 业务处理
```

### 正确的流程
```
HKSTT服务器 → JSON-RPC通知 → JsonRpcNotificationManager → 用户回调
```

## 📋 具体修复方案

### 1. 移除WebSDK中的业务方法

**已移除的方法**：
- `handleFaceStatusChange()`
- `handleNewUserDetected()`

**原因**：这些不应该在SDK核心层处理

### 2. 使用标准JSON-RPC通知

根据 `docs/json-rpc.md` 文档，正确的通知格式：

```json
// 人脸状态通知
{
  "jsonrpc": "2.0",
  "method": "notifications/faceStatus",
  "params": {
    "hasFace": true
  }
}

// 新用户检测通知
{
  "jsonrpc": "2.0", 
  "method": "notifications/newUser",
  "params": {
    "userId": "user_123",
    "timestamp": 1640995200000
  }
}
```

### 3. 用户正确的使用方式

```typescript
// ❌ 错误方式（当前）
// WebSDK内部直接处理业务逻辑

// ✅ 正确方式（重构后）
sdk.onNotification('notifications/faceStatus', (params) => {
  console.log('人脸状态变化:', params.hasFace);
  // 用户自定义处理逻辑
});

sdk.onNotification('notifications/newUser', (params) => {
  console.log('检测到新用户:', params.userId);
  // 用户自定义处理逻辑
});
```

## 🔧 轻量级重构步骤

### 第一步：清理WebSDK职责 ✅
- 移除 `handleFaceStatusChange`
- 移除 `handleNewUserDetected`
- 添加TODO注释说明正确做法

### 第二步：确保JSON-RPC通知机制完整
检查 `JsonRpcNotificationManager` 是否正确处理：
- `notifications/faceStatus`
- `notifications/newUser`
- `notifications/modelStatus`
- `notifications/asrOfflineResult`

### 第三步：更新文档和示例
- 更新使用指南
- 提供正确的通知监听示例
- 说明迁移路径

### 第四步：测试验证
- 验证通知机制工作正常
- 确保用户可以正确监听通知
- 测试通知的生命周期管理

## 📚 架构原则

### 1. 分层职责
```
┌─────────────────┐
│   用户业务层     │ ← 处理具体业务逻辑
├─────────────────┤
│   WebSDK层      │ ← 只负责组件协调和生命周期
├─────────────────┤
│  JSON-RPC层     │ ← 处理协议和消息路由
├─────────────────┤
│   传输层        │ ← 处理网络通信
└─────────────────┘
```

### 2. 单一职责
- **WebSDK**: 组件初始化、生命周期管理、配置管理
- **JsonRpcNotificationManager**: 通知接收、分发、回调管理
- **JsonRpcRequestManager**: 请求发送、响应匹配、ID管理
- **JsonRpcActionHandler**: Action处理、UI操作分发

### 3. 依赖倒置
- 上层不应该依赖下层的具体实现
- 通过接口和事件进行解耦
- 业务逻辑应该在用户层，不在SDK层

## 🎯 重构后的好处

### 1. 符合JSON-RPC规范
- 所有通知都通过标准的JSON-RPC机制处理
- 用户可以统一使用 `onNotification` API

### 2. 职责清晰
- WebSDK专注于SDK管理
- 业务逻辑由用户控制
- 更好的可测试性

### 3. 扩展性强
- 新增通知类型无需修改WebSDK
- 用户可以灵活处理各种通知
- 更容易维护和升级

### 4. 向后兼容
- 现有的JSON-RPC功能不受影响
- 渐进式迁移，不破坏现有代码
- 提供迁移指南

## 🚀 下一步行动

1. **验证JsonRpcNotificationManager** - 确保通知机制完整
2. **更新使用文档** - 提供正确的使用示例
3. **添加迁移指南** - 帮助用户从旧方式迁移
4. **完善测试** - 确保通知机制的可靠性

这样的重构既解决了架构问题，又保持了轻量级的特点，符合JSON-RPC规范，提高了代码的可维护性。
