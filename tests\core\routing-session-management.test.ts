/**
 * 路由和会话管理测试
 * 验证修复后的路由决策逻辑和会话ID管理
 */

import { describe, it, expect, beforeEach } from 'vitest';

import { EventBus } from '../core/EventBus';
import { MessageRouter } from '../routing/MessageRouter';
import { generateUUID, generateTypedRequestId } from '../utils/helpers';

describe('路由和会话管理修复验证', () => {
  let eventBus: EventBus;
  let messageRouter: MessageRouter;

  beforeEach(() => {
    eventBus = new EventBus();
    messageRouter = new MessageRouter({ defaultScenario: 'custom-component' }, eventBus);
  });

  describe('UUID生成功能', () => {
    it('应该生成唯一的UUID', () => {
      const uuid1 = generateUUID();
      const uuid2 = generateUUID();

      expect(uuid1).toBeDefined();
      expect(uuid2).toBeDefined();
      expect(uuid1).not.toBe(uuid2);
      expect(typeof uuid1).toBe('string');
      expect(uuid1.length).toBeGreaterThan(0);
    });

    it('应该生成带类型前缀的请求ID', () => {
      const asrId = generateTypedRequestId('asr');
      const chatId = generateTypedRequestId('chat');

      expect(asrId).toMatch(/^asr-/);
      expect(chatId).toMatch(/^chat-/);
      expect(asrId).not.toBe(chatId);
    });
  });

  describe('ASR路由决策', () => {
    it('应该基于页面可见性进行智能路由', () => {
      const mockASRData = { sid: 'test-sid', text: '你好' };
      const routedEvents: any[] = [];

      // 监听路由事件
      eventBus.on('custom-component:user-input', data => {
        routedEvents.push({ target: 'custom-component', data });
      });

      eventBus.on('greeting-page:user-input', data => {
        routedEvents.push({ target: 'greeting-page', data });
      });

      // 模拟ASR结果
      eventBus.emit('hkstt:asr-offline-result', mockASRData);

      expect(routedEvents).toHaveLength(1);
      expect(routedEvents[0].target).toBe('custom-component');
      expect(routedEvents[0].data.userInput).toBe('你好');
      expect(routedEvents[0].data.sessionId).toBeDefined();
      expect(routedEvents[0].data.requestId).toMatch(/^asr-/);
    });

    it('应该在打招呼页面可见时路由到打招呼页面', () => {
      const mockASRData = { sid: 'test-sid', text: '你好' };
      const routedEvents: any[] = [];

      // 设置打招呼页面可见
      eventBus.emit('component:greeting-page-visible', {});

      // 监听路由事件
      eventBus.on('greeting-page:user-input', data => {
        routedEvents.push({ target: 'greeting-page', data });
      });

      // 模拟ASR结果
      eventBus.emit('hkstt:asr-offline-result', mockASRData);

      expect(routedEvents).toHaveLength(1);
      expect(routedEvents[0].target).toBe('greeting-page');
      expect(routedEvents[0].data.sessionId).toMatch(/^greeting-/);
    });
  });

  describe('AI响应路由', () => {
    it('应该严格按照sessionId进行精确路由', () => {
      const customSessionId = 'custom-test-session-123';
      const greetingSessionId = 'greeting-test-session-456';
      const routedEvents: any[] = [];

      // 监听路由事件
      eventBus.on('custom-component:ai-response', data => {
        routedEvents.push({ target: 'custom-component', data });
      });

      eventBus.on('greeting-page:ai-response', data => {
        routedEvents.push({ target: 'greeting-page', data });
      });

      // 模拟AI响应（自定义组件）
      eventBus.emit('ai:chat-final-response', {
        message: '这是给自定义组件的响应',
        sessionId: customSessionId,
        requestId: 'test-req-1',
      });

      // 模拟AI响应（打招呼页面）
      eventBus.emit('ai:chat-final-response', {
        message: '这是给打招呼页面的响应',
        sessionId: greetingSessionId,
        requestId: 'test-req-2',
      });

      expect(routedEvents).toHaveLength(2);

      // 验证路由到正确的组件
      const customEvent = routedEvents.find(e => e.data.sessionId === customSessionId);
      const greetingEvent = routedEvents.find(e => e.data.sessionId === greetingSessionId);

      expect(customEvent.target).toBe('custom-component');
      expect(greetingEvent.target).toBe('greeting-page');
    });
  });

  describe('会话ID管理', () => {
    it('应该为不同组件维护独立的会话ID', () => {
      const sessionIds: string[] = [];

      // 监听会话切换事件
      eventBus.on('router:scenario-changed', (data: any) => {
        sessionIds.push(data.sessionId);
      });

      // 切换到自定义组件
      messageRouter.switchScenario('custom-component');

      // 切换到打招呼页面
      messageRouter.switchScenario('greeting-page');

      expect(sessionIds).toHaveLength(2);
      expect(sessionIds[0]).toMatch(/^custom-/);
      expect(sessionIds[1]).toMatch(/^greeting-/);
      expect(sessionIds[0]).not.toBe(sessionIds[1]);
    });

    it('应该在组件隐藏时清理会话ID', () => {
      // 先切换到打招呼页面创建会话
      messageRouter.switchScenario('greeting-page');

      // 获取调试信息验证会话存在
      const debugInfo = messageRouter.getDebugInfo();
      expect(debugInfo.routingState.sessionId).toMatch(/^greeting-/);

      // 模拟打招呼页面隐藏
      eventBus.emit('component:greeting-page-hidden', {});

      // 验证会话被清理（这里我们无法直接访问私有属性，但可以通过行为验证）
      // 实际项目中可以添加公共方法来检查会话状态
    });
  });

  describe('内外部会话ID映射机制', () => {
    it('应该为AI服务使用纯UUID，为内部路由使用前缀ID', () => {
      const aiInternalRequests: any[] = [];
      const userInputEvents: any[] = [];

      // 监听内部AI请求（经过转换的）
      eventBus.on('ai:send-chat-request-internal', data => {
        aiInternalRequests.push(data);
      });

      // 监听用户输入事件
      eventBus.on('custom-component:user-input', data => {
        userInputEvents.push(data);
      });

      // 模拟ASR结果
      eventBus.emit('hkstt:asr-offline-result', {
        sid: 'test-sid',
        text: '测试消息',
      });

      // 验证会话ID映射
      expect(aiInternalRequests).toHaveLength(1);
      expect(userInputEvents).toHaveLength(1);

      // 内部事件使用带前缀的会话ID
      expect(userInputEvents[0].sessionId).toMatch(/^custom-/);

      // AI内部请求使用纯UUID（无前缀）
      const aiSessionId = aiInternalRequests[0].sessionId;
      expect(aiSessionId).toBeDefined();
      expect(aiSessionId).not.toMatch(/^custom-/);
      expect(aiSessionId).not.toMatch(/^greeting-/);
      expect(aiSessionId).not.toMatch(/session_/);

      // 请求ID应该一致
      expect(aiInternalRequests[0].requestId).toBe(userInputEvents[0].requestId);
    });

    it('应该拦截组件发送的AI请求并转换会话ID', () => {
      const aiInternalRequests: any[] = [];

      // 监听拦截器处理后的内部请求
      eventBus.on('ai:send-chat-request-internal', data => {
        aiInternalRequests.push(data);
      });

      // 模拟组件发送带前缀的AI请求
      eventBus.emit('ai:send-chat-request', {
        userInput: '用户手动输入的消息',
        sessionId: 'custom-test-session-123',
        requestId: 'manual-req-1',
      });

      // 验证拦截和转换
      expect(aiInternalRequests).toHaveLength(1);

      // 转换后的请求应该使用纯UUID
      const convertedRequest = aiInternalRequests[0];
      expect(convertedRequest.sessionId).toBeDefined();
      expect(convertedRequest.sessionId).not.toMatch(/^custom-/);
      expect(convertedRequest.sessionId).not.toMatch(/^greeting-/);
      expect(convertedRequest.userInput).toBe('用户手动输入的消息');
      expect(convertedRequest.requestId).toBe('manual-req-1');
    });
  });
});
