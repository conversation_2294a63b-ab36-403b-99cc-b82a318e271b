/**
 * 增强的JSON-RPC功能测试
 * 验证向后兼容性和JSON-RPC 2.0格式规范
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';

import { EventBus } from '../core/EventBus';

describe('增强的JSON-RPC功能测试', () => {
  let eventBus: EventBus;

  beforeEach(() => {
    eventBus = new EventBus();
  });

  afterEach(() => {
    eventBus.clear();
  });

  describe('EventBus基础功能测试', () => {
    it('应该能够正确发送和接收事件', () => {
      const receivedEvents: any[] = [];

      // 注册事件监听器
      eventBus.on('test:event', data => {
        receivedEvents.push(data);
      });

      // 发送事件
      eventBus.emit('test:event', { message: 'test data' });

      // 验证事件被接收
      expect(receivedEvents).toHaveLength(1);
      expect(receivedEvents[0]).toEqual({ message: 'test data' });
    });

    it('应该支持多个监听器', () => {
      const listener1Events: any[] = [];
      const listener2Events: any[] = [];

      // 注册多个监听器
      eventBus.on('test:multi', data => {
        listener1Events.push(data);
      });

      eventBus.on('test:multi', data => {
        listener2Events.push(data);
      });

      // 发送事件
      eventBus.emit('test:multi', { id: 1 });

      // 验证所有监听器都收到事件
      expect(listener1Events).toHaveLength(1);
      expect(listener2Events).toHaveLength(1);
      expect(listener1Events[0]).toEqual({ id: 1 });
      expect(listener2Events[0]).toEqual({ id: 1 });
    });
  });

  describe('JSON-RPC 2.0格式验证', () => {
    it('应该验证有效的JSON-RPC 2.0请求格式', () => {
      const validRequest = {
        jsonrpc: '2.0',
        method: 'test.method',
        params: { param1: 'value1' },
        id: 'req_001',
      };

      // 验证请求格式
      expect(validRequest.jsonrpc).toBe('2.0');
      expect(typeof validRequest.method).toBe('string');
      expect(validRequest.id).toBeDefined();
    });

    it('应该验证有效的JSON-RPC 2.0成功响应格式', () => {
      const validSuccessResponse = {
        jsonrpc: '2.0',
        result: { success: true, data: 'test' },
        id: 'req_001',
      };

      // 验证成功响应格式
      expect(validSuccessResponse.jsonrpc).toBe('2.0');
      expect('result' in validSuccessResponse).toBe(true);
      expect('error' in validSuccessResponse).toBe(false);
      expect(validSuccessResponse.id).toBeDefined();
    });

    it('应该验证有效的JSON-RPC 2.0错误响应格式', () => {
      const validErrorResponse = {
        jsonrpc: '2.0',
        error: {
          code: -32601,
          message: 'Method not found',
          data: { method: 'invalid.method' },
        },
        id: 'req_001',
      };

      // 验证错误响应格式
      expect(validErrorResponse.jsonrpc).toBe('2.0');
      expect('error' in validErrorResponse).toBe(true);
      expect('result' in validErrorResponse).toBe(false);
      expect(typeof validErrorResponse.error.code).toBe('number');
      expect(typeof validErrorResponse.error.message).toBe('string');
      expect(validErrorResponse.id).toBeDefined();
    });
  });

  describe('AI Action格式验证', () => {
    it('应该验证业务操作action格式', () => {
      const loginActionResponse = {
        jsonrpc: '2.0',
        result: {
          message: '请先登录您的账号',
          action: 'login',
          data: { type: 'idCard' },
        },
        id: 'req_001',
      };

      // 验证action响应格式
      expect(loginActionResponse.result.action).toBe('login');
      expect(loginActionResponse.result.message).toBeDefined();
      expect(loginActionResponse.result.data).toBeDefined();
    });

    it('应该测试统一的onAction监听器', () => {
      const actionResults: any[] = [];

      // 模拟onAction回调
      const mockCallback = (result: any) => {
        actionResults.push(result);
      };

      // 测试不同类型的action结果
      const loginResult = {
        message: '请先登录您的账号',
        action: 'login',
        data: { type: 'idCard' },
      };

      const updateResult = {
        message: '正在为您填写用户信息',
        action: 'update',
        type: 'form',
        data: { name: '李明', phone: '13800138000' },
      };

      // 模拟调用回调
      mockCallback(loginResult);
      mockCallback(updateResult);

      // 验证回调接收到完整的结果对象
      expect(actionResults).toHaveLength(2);
      expect(actionResults[0]).toEqual(loginResult);
      expect(actionResults[1]).toEqual(updateResult);
    });

    it('应该验证数据更新action格式', () => {
      const updateActionResponse = {
        jsonrpc: '2.0',
        result: {
          message: '正在为您填写用户信息',
          action: 'update',
          type: 'form',
          data: {
            name: '李明',
            phone: '13800138000',
          },
        },
        id: 'req_002',
      };

      // 验证更新action格式
      expect(updateActionResponse.result.action).toBe('update');
      expect(updateActionResponse.result.type).toBe('form');
      expect(updateActionResponse.result.data).toBeDefined();
    });
  });

  describe('文档中定义的方法格式验证', () => {
    it('应该验证speak方法请求格式', () => {
      const speakRequest = {
        jsonrpc: '2.0',
        method: 'speak',
        params: {
          text: '您好，欢迎使用我们的服务',
          delay: 1000,
          display: true,
        },
        id: 'req_speak_001',
      };

      // 验证speak请求格式（根据文档修正）
      expect(speakRequest.method).toBe('speak');
      expect(speakRequest.params.text).toBeDefined();
      expect(typeof speakRequest.params.text).toBe('string');
      expect(typeof speakRequest.params.delay).toBe('number');
      expect(typeof speakRequest.params.display).toBe('boolean');
    });

    it('应该验证updateBackgroundInfo方法请求格式', () => {
      const updateBgRequest = {
        jsonrpc: '2.0',
        method: 'updateBackgroundInfo',
        params: {
          userInfo: {
            name: '张三',
            age: 30,
            occupation: '工程师',
          },
          context: '用户咨询业务办理',
        },
        id: 'req_update_001',
      };

      // 验证updateBackgroundInfo请求格式
      expect(updateBgRequest.method).toBe('updateBackgroundInfo');
      expect(updateBgRequest.params.userInfo).toBeDefined();
      expect(typeof updateBgRequest.params.userInfo).toBe('object');
    });

    it('应该验证addMessages方法请求格式', () => {
      const addMessagesRequest = {
        jsonrpc: '2.0',
        method: 'addMessages',
        params: {
          messages: [
            { role: 'user', content: '我想查询余额' },
            { role: 'assistant', content: '我来帮您查询账户余额' },
          ],
        },
        id: 'req_messages_001',
      };

      // 验证addMessages请求格式
      expect(addMessagesRequest.method).toBe('addMessages');
      expect(Array.isArray(addMessagesRequest.params.messages)).toBe(true);
      expect(addMessagesRequest.params.messages.length).toBeGreaterThan(0);
    });

    it('应该验证pushBizData方法请求格式', () => {
      const pushBizDataRequest = {
        jsonrpc: '2.0',
        method: 'pushBizData',
        params: {
          key: 'userProfile', // 根据文档修正：应该是key而不是type
          data: {
            userId: '12345',
            accountType: 'premium',
            balance: 1000.5,
          },
        },
        id: 'req_bizdata_001',
      };

      // 验证pushBizData请求格式（根据文档修正）
      expect(pushBizDataRequest.method).toBe('pushBizData');
      expect(pushBizDataRequest.params.key).toBeDefined();
      expect(pushBizDataRequest.params.data).toBeDefined();
      expect(typeof pushBizDataRequest.params.key).toBe('string');
    });
  });

  describe('特殊通知格式验证', () => {
    it('应该验证流式响应通知格式', () => {
      const progressNotification = {
        jsonrpc: '2.0',
        method: 'notifications/progress',
        params: {
          message: '你好，',
          requestId: 'req_003',
        },
      };

      // 验证流式通知格式
      expect(progressNotification.method).toBe('notifications/progress');
      expect(progressNotification.params.message).toBeDefined();
      expect(progressNotification.params.requestId).toBeDefined();
    });

    it('应该验证用户输入通知格式', () => {
      const userInputNotification = {
        jsonrpc: '2.0',
        method: 'notifications/userInput',
        params: {
          userInput: '我想改密码',
          requestId: 'req_004',
          sessionId: 'session_001',
        },
      };

      // 验证用户输入通知格式
      expect(userInputNotification.method).toBe('notifications/userInput');
      expect(userInputNotification.params.userInput).toBeDefined();
      expect(userInputNotification.params.requestId).toBeDefined();
      expect(userInputNotification.params.sessionId).toBeDefined();
    });
  });
});
