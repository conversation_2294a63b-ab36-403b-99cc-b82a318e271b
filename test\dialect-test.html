<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>方言切换测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary { background: #007AFF; color: white; }
        .btn-success { background: #34C759; color: white; }
        .btn-warning { background: #FF9500; color: white; }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            background: #f0f0f0;
        }
        .log {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🗣️ 方言切换功能测试</h1>
    
    <div class="test-section">
        <h3>SDK初始化</h3>
        <button class="btn-primary" onclick="initSDK()">初始化SDK</button>
        <div id="sdkStatus" class="status">未初始化</div>
    </div>

    <div class="test-section">
        <h3>方言切换测试</h3>
        <button class="btn-success" onclick="switchToMandarin()">切换到普通话</button>
        <button class="btn-warning" onclick="switchToSichuan()">切换到川渝话</button>
        <div id="languageStatus" class="status">当前语言: 普通话</div>
    </div>

    <div class="test-section">
        <h3>TTS测试</h3>
        <input type="text" id="testText" value="你好，欢迎使用WebSDK！" style="width: 300px; padding: 8px;">
        <button class="btn-primary" onclick="testTTS()">开始TTS测试</button>
        <div id="ttsStatus" class="status">TTS状态: 空闲</div>
    </div>

    <div class="test-section">
        <h3>测试日志</h3>
        <button class="btn-primary" onclick="clearLog()">清空日志</button>
        <div id="log" class="log"></div>
    </div>

    <script src="../dist/web-service-sdk.js"></script>
    <script>
        let sdk = null;
        let currentLanguage = 'mandarin';

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.style.color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black';
            entry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function updateSDKStatus(status) {
            document.getElementById('sdkStatus').textContent = status;
        }

        function updateLanguageStatus(language) {
            document.getElementById('languageStatus').textContent = `当前语言: ${language === 'sichuan' ? '川渝话' : '普通话'}`;
        }

        function updateTTSStatus(status) {
            document.getElementById('ttsStatus').textContent = `TTS状态: ${status}`;
        }

        async function initSDK() {
            try {
                log('开始初始化SDK...');
                updateSDKStatus('正在初始化...');

                sdk = new WebServiceSDK({
                    wsUrl: 'ws://localhost:8002/ws',
                    httpUrl: 'http://localhost:8002',
                    ttsUrl: 'http://*************:8080',
                    debug: true
                });

                // 监听TTS状态变化
                sdk.getEventBus().on('tts:status-change', (data) => {
                    log(`TTS状态变化: ${data.status}`);
                    updateTTSStatus(data.status);
                });

                // 监听语言变化事件
                sdk.getEventBus().on('tts:language-change', (data) => {
                    log(`语言切换事件: ${data.language}`);
                    currentLanguage = data.language;
                    updateLanguageStatus(data.language);
                });

                await sdk.start();
                updateSDKStatus('已初始化');
                log('SDK初始化成功', 'info');

            } catch (error) {
                log(`SDK初始化失败: ${error.message}`, 'error');
                updateSDKStatus('初始化失败');
            }
        }

        function switchToMandarin() {
            if (!sdk) {
                log('请先初始化SDK', 'warn');
                return;
            }

            log('切换到普通话');
            sdk.getEventBus().emit('tts:language-change', { language: 'mandarin' });
            currentLanguage = 'mandarin';
            updateLanguageStatus('mandarin');
        }

        function switchToSichuan() {
            if (!sdk) {
                log('请先初始化SDK', 'warn');
                return;
            }

            log('切换到川渝话');
            sdk.getEventBus().emit('tts:language-change', { language: 'sichuan' });
            currentLanguage = 'sichuan';
            updateLanguageStatus('sichuan');
        }

        async function testTTS() {
            if (!sdk) {
                log('请先初始化SDK', 'warn');
                return;
            }

            const text = document.getElementById('testText').value;
            if (!text.trim()) {
                log('请输入测试文本', 'warn');
                return;
            }

            try {
                log(`开始TTS测试 - 语言: ${currentLanguage}, 文本: ${text}`);
                
                // 根据当前语言选择指令
                const instruction = currentLanguage === 'sichuan' ? '用四川话说这句话' : '用自然清晰的普通话说话';
                log(`使用指令: ${instruction}`);

                // 直接调用TTS服务
                await sdk.speak(text, instruction);
                
                log('TTS请求已发送');

            } catch (error) {
                log(`TTS测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', () => {
            log('方言切换测试页面已加载');
            log('请先点击"初始化SDK"按钮');
        });
    </script>
</body>
</html>
